terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~>5.0"
    }
    gitlab = {
      source = "gitlabhq/gitlab"
    }
    http = {
      source = "hashicorp/http"
    }
    local = {
      source = "hashicorp/local"
    }
    null = {
      source = "hashicorp/null"
    }
  }

  # backend "http" {}
}

provider "aws" {
  region     = var.AWS_REGION
  access_key = var.AWS_ACCESS_KEY_ID
  secret_key = var.AWS_SECRET_ACCESS_KEY

  default_tags {
    tags = local.DEFAULT_TAGS
  }
}

# Second AWS provider is required for ACM
provider "aws" {
  alias  = "virginia"
  region = "us-east-1"
  access_key = var.AWS_ACCESS_KEY_ID
  secret_key = var.AWS_SECRET_ACCESS_KEY

  default_tags {
    tags = local.DEFAULT_TAGS
  }
}

provider "gitlab" {
  token    = var.GITLAB_ACCESS_TOKEN
  base_url = var.GITLAB_BASEURL
}
