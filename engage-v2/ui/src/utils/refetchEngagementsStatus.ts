export const createRefetchStatus =
  <T = unknown>(
    collectionKey: string,
    options: { refetchIntervalSeconds?: number; statusField?: string } = {},
  ) =>
  (data: unknown): number | false => {
    const _interval = options.refetchIntervalSeconds ?? 30000;
    const statusField = options.statusField;

    // Try to extract the collection
    let collection: unknown[] | undefined;

    if (Array.isArray(data)) {
      collection = data;
    } else if (data && typeof data === 'object' && 'state' in data) {
      const stateData = (data as { state?: { data?: Record<string, unknown> | unknown[] } }).state?.data;
      if (stateData && typeof stateData === 'object' && collectionKey in stateData) {
        const collectionData = (stateData as Record<string, unknown>)[collectionKey];
        if (Array.isArray(collectionData)) {
          collection = collectionData;
        }
      } else if (Array.isArray(stateData)) {
        collection = stateData;
      }
    }

    if (!collection || !Array.isArray(collection)) {
      return _interval;
    }

    const allowedStatuses = ["SUCCESS", "ACTIVE", "ERROR", "WARNING"];
    const shouldRefetch = collection.some((item: unknown) => {
      const status = statusField && item && typeof item === 'object'
        ? String((item as Record<string, unknown>)[statusField] ?? '')
        : '';
      return status && !allowedStatuses.includes(status);
    });

    return shouldRefetch ? _interval : false;
  };

// Specialized helper for cloud instance state polling on engagement page
export const createRefetchCloudInstances = (options: { refetchIntervalMs?: number; transientStates?: string[] } = {}) => {
  const interval = options.refetchIntervalMs ?? 3000;
  const transient = new Set((options.transientStates ?? ["pending", "stopping", "shutting-down"]).map((s) => s.toLowerCase()));
  return (data: unknown): number | false => {
    // Try common shapes
    const dataObj = data as Record<string, unknown>;
    const groups =
      dataObj?.cloud_instance_node_groups ||
      ((dataObj?.state as Record<string, unknown>)?.data as Record<string, unknown>)?.cloud_instance_node_groups ||
      [];

    if (!Array.isArray(groups)) return interval;

    for (const ng of groups) {
      const nodeGroup = ng as Record<string, unknown>;
      const cis = nodeGroup?.cloud_instances || [];
      if (Array.isArray(cis)) {
        for (const ci of cis) {
          const cloudInstance = ci as Record<string, unknown>;
          const s = String(cloudInstance?.cloud_instance_state || "").toLowerCase();
          if (transient.has(s)) return interval;
        }
      }
    }
    return false;
  };
};
