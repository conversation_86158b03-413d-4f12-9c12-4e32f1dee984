/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { CreateNodeTypeHostInputBodyAlternativeNames } from "./createNodeTypeHostInputBodyAlternativeNames";
import type { CreateNodeTypeHostInputBodyIpAddresses } from "./createNodeTypeHostInputBodyIpAddresses";

export interface CreateNodeTypeHostInputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  alternative_names?: CreateNodeTypeHostInputBodyAlternativeNames;
  /** Engagement ID */
  engagement_id: string;
  ip_addresses?: CreateNodeTypeHostInputBodyIpAddresses;
  /** @maxLength 100 */
  name: string;
  /** Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created */
  node_group_id?: string;
}
