# Async Cloud Instance State Tracking Integration Guide

## Overview

This guide explains how to integrate the RabbitMQ-based async state tracking system to fix the current IP address tracking issues in cloud instance actions.

## Current Problems

1. **Immediate DB Queries**: Actions query database immediately, but AWS operations are async
2. **Stale State**: Database shows "pending" while AWS shows "running" 
3. **Race Conditions**: REAPI waits for AWS changes but queries stale database
4. **Sync Delay**: Cloud sync happens every 60 minutes, causing inconsistent state

## Solution Architecture

```
Cloud Instance Action → Immediate Response → Queue State Check Task → Background Worker → Verify AWS State → Update DB → Log Accurate IP Changes
```

## Integration Steps

### Step 1: Add State Check Worker to Main Application

Add to `engage-v2/api/cmd/api/main.go`:

```go
import (
    "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/scheduler/cloudinstance"
)

// Add after existing RabbitMQ workers (around line 548)
// Start cloud instance state check worker
stateCheckCh, err := app.getChannel()
if err != nil {
    app.logger.Error("Failed to get RabbitMQ channel for cloud instance state check worker", "error", err.Error())
} else {
    cloudinstance.StartStateCheckWorker(stateCheckCh, "cloud-instance-state-check", app.queries, app.logger)
}
```

### Step 2: Implement AWS Client Function

Add to `engage-v2/api/internal/scheduler/cloudinstance/statetracker.go`:

```go
import (
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/credentials"
    "github.com/aws/aws-sdk-go-v2/service/ec2"
)

// getAWSClientForAccount creates an AWS EC2 client for the given account and region
func getAWSClientForAccount(accountID, region string) (*ec2.Client, error) {
    // Get credentials from your existing secret manager
    sm, err := keys.NewSecretsManager("us-east-1") // or your default region
    if err != nil {
        return nil, fmt.Errorf("failed to create secrets manager: %w", err)
    }
    
    secretValues, err := sm.GetSecret(accountID)
    if err != nil {
        return nil, fmt.Errorf("failed to get secret for account %s: %w", accountID, err)
    }
    
    // Parse credentials (adapt based on your secret structure)
    accessKey := secretValues["access_key_id"]
    secretKey := secretValues["access_key_secret"]
    
    cfg, err := config.LoadDefaultConfig(context.TODO(),
        config.WithRegion(region),
        config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
            accessKey, secretKey, "",
        )),
    )
    if err != nil {
        return nil, fmt.Errorf("failed to load AWS config: %w", err)
    }
    
    return ec2.NewFromConfig(cfg), nil
}
```

### Step 3: Update Cloud Instance Actions

Replace immediate IP tracking with async tasks in `engage-v2/api/cmd/api/nodeTypeCloudInstance.go`:

```go
import (
    "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/scheduler/cloudinstance"
)

// Example for REAPI action - replace the current IP tracking code with:
func handleREAPIAction(ctx context.Context, i *struct{ NodeID string }, a *application) (*awsActionOutput, error) {
    // ... existing REAPI logic ...
    
    // Get node details for state check task
    nodeIDPg, _ := converters.StringToPgTypeUUID(i.NodeID)
    userID, _ := ctx.Value("userID").(string)
    userIDPg, _ := converters.StringToPgTypeUUID(userID)
    
    node, err := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{
        NodeID: *nodeIDPg,
        ID:     *userIDPg,
    })
    if err != nil {
        return nil, huma.Error500InternalServerError("Failed to get node details")
    }
    
    // Get current IP for comparison
    var oldIP string
    if node.PublicIpv4Address != nil {
        oldIP = node.PublicIpv4Address.String()
    }
    
    // Queue async state check task
    stateCheckTask := cloudinstance.CloudInstanceStateCheckTask{
        NodeID:        i.NodeID,
        InstanceID:    instanceID,
        Action:        "reapi",
        UserID:        userID,
        OldIP:         oldIP,
        ExpectedState: "running",
        Provider:      string(node.Provider),
        Region:        node.Region,
        AccountID:     node.AwsAccountID.String(), // Convert UUID to string
    }
    
    ch, err := a.getChannel()
    if err == nil {
        err = cloudinstance.PublishStateCheckTask(ctx, ch, "cloud-instance-state-check", stateCheckTask)
        if err != nil {
            a.logger.Error("Failed to queue state check task", "error", err)
        }
    }
    
    // Log immediate action (without IP details)
    logMessage := fmt.Sprintf("AWS instance REAPI requested (%s): stop+start initiated", instanceID)
    _ = activitylogs.InsertLog(a.queries, logMessage, db.LogsNodesTypeEnumNODEUPDATE, *userIDPg, *nodeIDPg, timestamp)
    
    return resp, nil
}
```

### Step 4: Update Other Actions

Apply similar changes to:
- Start action: Queue state check for IP assignment
- Stop action: Queue state check for final IP logging  
- Terminate action: Queue state check for final state
- Reboot action: Queue state check for IP confirmation

### Step 5: Test the Integration

1. **Start the Application**: Ensure RabbitMQ worker starts without errors
2. **Perform Actions**: Execute cloud instance actions (REAPI, start, stop)
3. **Check Logs**: Verify state check tasks are queued and processed
4. **Verify Timeline**: Confirm accurate IP tracking in activity timeline
5. **Monitor Queues**: Check RabbitMQ management UI for task processing

## Benefits of This Approach

1. **Accurate State**: Always reflects actual AWS state, not stale database
2. **No Race Conditions**: Async verification eliminates timing issues
3. **Better UX**: Immediate action response + accurate follow-up logging
4. **Scalable**: RabbitMQ handles task queuing and retry logic
5. **Consistent**: Works with existing cloud instance sync infrastructure

## Monitoring and Debugging

### RabbitMQ Queue Monitoring
- Queue: `cloud-instance-state-check`
- Monitor message rates and processing times
- Check for failed/requeued messages

### Log Monitoring
```bash
# Check for state check task processing
grep "Processing cloud instance state check task" /var/log/engage-api.log

# Check for IP change logging
grep "Logged IP change" /var/log/engage-api.log

# Check for errors
grep "Error processing cloud instance state check task" /var/log/engage-api.log
```

### Common Issues
1. **AWS Credentials**: Ensure account secrets are accessible
2. **Network**: Verify AWS API connectivity from workers
3. **Permissions**: Check AWS IAM permissions for EC2 describe operations
4. **Queue Backlog**: Monitor for task processing delays

## Rollback Plan

If issues occur, you can:
1. Stop the state check worker
2. Revert to immediate IP tracking (current implementation)
3. Clear the state check queue
4. Investigate and fix issues before re-enabling

The current implementation will continue to work (with known limitations) if the async system is disabled.
