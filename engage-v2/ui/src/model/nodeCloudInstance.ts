/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { NodeCloudInstanceCiDeploymentStatus } from "./nodeCloudInstanceCiDeploymentStatus";
import type { NodeCloudInstanceCloudInstanceState } from "./nodeCloudInstanceCloudInstanceState";
import type { NodeCloudInstanceOpenIngressTcpPorts } from "./nodeCloudInstanceOpenIngressTcpPorts";
import type { NodeCloudInstanceProvider } from "./nodeCloudInstanceProvider";
import type { NodeCloudInstancePublicIpv4Address } from "./nodeCloudInstancePublicIpv4Address";

export interface NodeCloudInstance {
  ci_deployment_status: NodeCloudInstanceCiDeploymentStatus;
  cloud_instance_id: string;
  cloud_instance_state: NodeCloudInstanceCloudInstanceState;
  /** AWS Instance type */
  instance_type: string;
  /**
   * Instance name
   * @minLength 1
   * @maxLength 256
   * @pattern ^[a-zA-Z0-9 _.:/=+\-@]{1,256}$
   */
  name: string;
  /** Node ID */
  node_id: string;
  /** Open ingress TCP ports */
  open_ingress_tcp_ports: NodeCloudInstanceOpenIngressTcpPorts;
  operating_system_image_id: string;
  provider: NodeCloudInstanceProvider;
  public_ipv4_address: NodeCloudInstancePublicIpv4Address;
  region: string;
}
