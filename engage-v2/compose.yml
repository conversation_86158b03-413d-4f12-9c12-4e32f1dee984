services:
  rabbitmq:
    image: rabbitmq:4-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 30s
      timeout: 30s
      retries: 3
      start_period: 30s
    restart: unless-stopped # or always / on-failure:3
    stop_grace_period: 30s
    ulimits:
      nofile:
        soft: 65536
        hard: 65536

  db:
    image: postgres:17
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=engage
      - POSTGRES_USER=engage
      - POSTGRES_PASSWORD=engage
    volumes:
      - pgdata:/var/lib/postgresql/data

  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    depends_on:
      rabbitmq:
        condition: service_healthy
      db:
        condition: service_started
    environment:
      # Azure
      - AZURE_TENANT_ID=changeme
      - AZURE_APP_ID=changeme
      - AZURE_CLIENT_SECRET=changeme
      - STANDARD_USERS_GROUP_ID=changeme
      - ADMIN_USERS_GROUP_ID=changeme
      # CORS
      - CORS_URL=http://localhost:5173
      # Database
      - DB_USER=engage
      - DB_PASSWORD=engage
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=engage
      # RabbitMQ
      - RABBITMQ_USER=guest
      - RABBITMQ_PASSWORD=guest
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_PROTO=amqp
      # Terraform
      - TERRAFORM_EXEC_PATH=/usr/local/bin/terraform

volumes:
  pgdata:
  rabbitmq_data:
