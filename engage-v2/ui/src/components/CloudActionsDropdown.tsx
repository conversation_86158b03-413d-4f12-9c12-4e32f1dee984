import { <PERSON>u, <PERSON>u<PERSON><PERSON>on, MenuItem, MenuItems } from "@headlessui/react";
import { useQueryClient } from "@tanstack/react-query";
import { useMemo, useState } from "react";
import { HiChevronDown } from "react-icons/hi";
import { toast } from "react-toastify";
import {
  getGetEngagementCloudInstancesQueryKey,
  usePostNodesCloudInstanceReboot,
  usePostNodesCloudInstanceStop,
  usePostNodesCloudInstanceTerminate,
  usePostNodesCloudInstanceStart,
  usePostNodesCloudInstanceReapi,
} from "../client";
import ConfirmationModal from "./ConfirmationModal";

export default function CloudActionsDropdown({
  engagementId,
  selectedNodeIds,
  selectedStates,
}: {
  engagementId: string;
  selectedNodeIds: string[];
  selectedStates?: string[]; // optional: states of selected instances
}) {
  const queryClient = useQueryClient();
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [isReapiConfirmOpen, setIsReapiConfirmOpen] = useState(false);


  const { mutateAsync: stopAsync, isPending: isStopping } =
    usePostNodesCloudInstanceStop();
  const { mutateAsync: rebootAsync, isPending: isRebooting } =
    usePostNodesCloudInstanceReboot();
  const { mutateAsync: terminateAsync, isPending: isTerminating } =
    usePostNodesCloudInstanceTerminate();
  const { mutateAsync: startAsync, isPending: isStarting } =
    usePostNodesCloudInstanceStart();
  const { mutateAsync: reapiAsync, isPending: isReapi } =
    usePostNodesCloudInstanceReapi();

  const disabledGlobal =
    selectedNodeIds.length === 0 || isStopping || isRebooting || isTerminating || isStarting || isReapi;

  // Disable based on states if provided
  const { canStop, canReboot, canTerminate, canStart, canReapi } = useMemo(() => {
    if (!selectedStates || selectedStates.length === 0) {
      return {
        canStop: !disabledGlobal,
        canReboot: !disabledGlobal,
        canTerminate: !disabledGlobal,
        canStart: !disabledGlobal,
        canReapi: !disabledGlobal,
      };
    }
    const lc = selectedStates.map((s) => (s || "").toLowerCase());
    const allRunningOrPending = lc.every((s) => ["running", "pending"].includes(s));
    const noneTerminated = lc.every((s) => s !== "terminated" && s !== "shutting-down");
    return {
      canStop: !disabledGlobal && lc.some((s) => s === "running"),
      canReboot: !disabledGlobal && allRunningOrPending,
      canTerminate: !disabledGlobal && noneTerminated,
      canStart: !disabledGlobal && lc.every((s) => s === "stopped"),
      canReapi: !disabledGlobal && lc.every((s) => ["running", "stopped"].includes(s)),
    };
  }, [selectedStates, disabledGlobal]);

  const optimisticStateFor = (
    act: "stop" | "reboot" | "terminate" | "start" | "reapi",
  ) => {
    switch (act) {
      case "stop":
        return "stopping";
      case "start":
        return "pending";
      case "reboot":
        return "pending";
      case "terminate":
        return "shutting-down";
      case "reapi":
        return "stopping";
      default:
        return undefined;
    }
  };

  const doAction = async (
    action: "stop" | "reboot" | "terminate" | "start" | "reapi",
  ) => {
    if (selectedNodeIds.length === 0) return;
    const actionTitle = action.charAt(0).toUpperCase() + action.slice(1);
    // Immediate reassurance without a persistent loader; table shows progress spinners
    toast.success(`${actionTitle} requested for ${selectedNodeIds.length} instance(s)`, {
      autoClose: 1500,
    });
    try {
      // Fire all requests in parallel for faster UX
      const requests = selectedNodeIds.map((nodeID) => {
        if (action === "stop") return stopAsync({ nodeID });
        if (action === "reboot") return rebootAsync({ nodeID });
        if (action === "terminate") return terminateAsync({ nodeID });
        if (action === "start") return startAsync({ nodeID });
        if (action === "reapi") return reapiAsync({ nodeID });
        return Promise.resolve();
      });
      await Promise.allSettled(requests);

      // Optimistically set transient state for selected rows to kick off polling UI
      const key = getGetEngagementCloudInstancesQueryKey(engagementId);
      const optimistic = optimisticStateFor(action);
      if (optimistic) {
        queryClient.setQueryData(key, (prev: any) => {
          if (!prev) return prev;
          const groups = [...(prev.cloud_instance_node_groups || [])];
          for (const ng of groups) {
            if (!ng?.cloud_instances) continue;
            for (const ci of ng.cloud_instances) {
              if (selectedNodeIds.includes(ci?.id)) {
                ci.cloud_instance_state = optimistic;
              }
            }
          }
          return { ...prev, cloud_instance_node_groups: groups };
        });
      }

      // Single immediate invalidate; ongoing polling is controlled by refetchInterval in the page
      await queryClient.invalidateQueries({ queryKey: key });

      toast.update(toastId, {
        render: `${actionTitle} requested for ${selectedNodeIds.length} instance(s)`,
        type: "success",
        isLoading: false,
        autoClose: 2000,
      });
    } catch (e) {
      toast.update(toastId, {
        render: `Failed to ${action} instance(s)`,
        type: "error",
        isLoading: false,
        autoClose: 4000,
      });
    }
  };

  return (
    <>
      <Menu as="div" className="relative inline-block text-left ml-2">
        <div>
          <MenuButton
            disabled={disabledGlobal}
            className={`inline-flex justify-center rounded-sm border border-gray-300 px-3 py-1 text-sm ${disabledGlobal ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:bg-slate-100"}`}
          >
            Actions
            <HiChevronDown className="ml-2 h-4 w-4" />
          </MenuButton>
        </div>
        <MenuItems className="absolute right-0 mt-2 w-48 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
          <div className="py-1">
            <MenuItem>
              {({ active }) => (
                <button
                  className={`${active ? "bg-gray-100" : ""} block w-full px-4 py-2 text-left text-sm ${!canStop ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={() => canStop && doAction("stop")}
                  disabled={!canStop}
                >
                  Stop
                </button>
              )}
            </MenuItem>
            <MenuItem>
              {({ active }) => (
                <button
                  className={`${active ? "bg-gray-100" : ""} block w-full px-4 py-2 text-left text-sm ${!canReboot ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={() => canReboot && doAction("reboot")}
                  disabled={!canReboot}
                >
                  Reboot
                </button>
              )}
            </MenuItem>
            <MenuItem>
              {({ active }) => (
                <button
                  className={`${active ? "bg-gray-100" : ""} block w-full px-4 py-2 text-left text-sm ${!canStart ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={() => canStart && doAction("start")}
                  disabled={!canStart}
                >
                  Start
                </button>
              )}
            </MenuItem>
            <MenuItem>
              {({ active }) => (
                <button
                  className={`${active ? "bg-gray-100" : ""} block w-full px-4 py-2 text-left text-sm ${!canReapi ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={() => canReapi && setIsReapiConfirmOpen(true)}
                  disabled={!canReapi}
                >
                  ReAPI (Stop + Start)
                </button>
              )}
            </MenuItem>
            <MenuItem>
              {({ active }) => (
                <button
                  className={`${active ? "bg-gray-100" : ""} block w-full px-4 py-2 text-left text-sm text-red-600 ${!canTerminate ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={() => canTerminate && setIsConfirmOpen(true)}
                  disabled={!canTerminate}
                >
                  Terminate
                </button>
              )}
            </MenuItem>
          </div>
        </MenuItems>
      </Menu>

      <ConfirmationModal
        isOpen={isConfirmOpen}
        closeModal={() => setIsConfirmOpen(false)}
        title="Terminate instances?"
        message="This will request termination for the selected instance(s). This action cannot be undone."
        confirmButtonText="Terminate"
        confirmButtonVariant="danger"
        onConfirm={() => doAction("terminate")}
      />
      <ConfirmationModal
        isOpen={isReapiConfirmOpen}
        closeModal={() => setIsReapiConfirmOpen(false)}
        title="ReAPI instances (Stop + Start)?"
        message="This will stop and then start the selected instance(s) and attempt to refresh their public IP."
        confirmButtonText="ReAPI"
        confirmButtonVariant="primary"
        onConfirm={() => doAction("reapi")}
      />
    </>
  );
}

