/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { CloudHostAlternativeNames } from "./cloudHostAlternativeNames";
import type { CloudHostIpAddresses } from "./cloudHostIpAddresses";

export interface CloudHost {
  alternative_names: CloudHostAlternativeNames;
  client_name: string;
  created_at: string;
  id: string;
  ip_addresses: CloudHostIpAddresses;
  name: string;
  title: string;
  type: string;
  updated_at: string;
}
