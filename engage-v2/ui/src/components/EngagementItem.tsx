import {
  <PERSON>u,
  <PERSON>u<PERSON><PERSON>on,
  MenuItem,
  MenuItems,
  Transition,
} from "@headlessui/react";
import { useQueryClient } from "@tanstack/react-query";
import { Link, useNavigate } from "@tanstack/react-router";
import { MultiSelectChangeEvent } from "primereact/multiselect";
import {
  ChangeEvent,
  Fragment,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { FaAws } from "react-icons/fa";
import { HiEllipsisVertical } from "react-icons/hi2";
import { VscAzure } from "react-icons/vsc";
import { toast } from "react-toastify";

import {
  getGetEngagementsQueryKey,
  getGetUsersQueryKey,
  useEditEngagement,
  useGetEngagementAwsAccounts,
  useGetEngagementAzureTenants,
  useGetUsers,
} from "../client";
import {
  AWSAccount,
  AzureTenant,
  Engagement,
  EngagementUser,
  EngagementUsers,
} from "../model";
import { getStatusColor, getStatusIcon } from "../utils/assets";
import EditEngagementModal from "./EditEngagement/EditEngagementModal";

type EngagementItemProps = {
  id: string;
  status: string;
  clientName: string;
  title: string;
  users: EngagementUsers;
  engagement: Engagement;
};

const getStatusDotStyle = (status: string) => {
  switch (status) {
    case "SUCCESS":
    case "ACTIVE":
    case "Enabled":
      return "bg-green-500";
    case "PENDING":
    case "IN-PROGRESS":
      return "bg-yellow-500";
    case "ERROR":
    case "FAILED":
      return "bg-red-500";
    default:
      return "bg-gray-400";
  }
};

type ProviderStatusIndicatorProps = {
  engagementId: string;
  provider: "AWS" | "AZURE";
};

const ProviderStatusIndicator = ({
  engagementId,
  provider,
}: ProviderStatusIndicatorProps) => {
  const [isTooltipVisible, setIsTooltipVisible] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const queryOptions = {
    query: {
      refetchInterval: false as const,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
    },
  };

  const awsQuery = useGetEngagementAwsAccounts(engagementId, queryOptions);
  const azureQuery = useGetEngagementAzureTenants(engagementId, queryOptions);

  const { data, isLoading } = provider === "AWS" ? awsQuery : azureQuery;

  const accounts = useMemo(() => {
    if (!data) return [];
    if (provider === "AWS" && "accounts" in data && data.accounts) {
      return data.accounts;
    }
    if (provider === "AZURE" && "tenants" in data && data.tenants) {
      return data.tenants;
    }
    return [];
  }, [data, provider]);

  const statuses = useMemo(() => {
    if (!accounts) return [];
    return (accounts as (AWSAccount | AzureTenant)[]).map(
      (acc) => acc.account_cloud_status,
    );
  }, [accounts]);

  const statusCounts = useMemo(() => {
    if (!statuses) return {};
    return statuses.reduce(
      (acc, status) => {
        const normalizedStatus = status || "PENDING";
        acc[normalizedStatus] = (acc[normalizedStatus] || 0) + 1;
        return acc;
      },
      {} as { [key: string]: number },
    );
  }, [statuses]);

  // Hook to handle clicks outside of the tooltip to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        tooltipRef.current &&
        !tooltipRef.current.contains(event.target as Node)
      ) {
        setIsTooltipVisible(false);
      }
    };

    if (isTooltipVisible) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isTooltipVisible]);

  if (isLoading && accounts.length === 0) {
    return (
      <div className="flex h-[48px] w-8 items-center justify-center">
        <svg
          className="h-4 w-4 animate-spin text-gray-400"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      </div>
    );
  }

  if (!isLoading && accounts.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-col items-center">
      <div
        ref={tooltipRef}
        className="group relative flex flex-col items-center"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setIsTooltipVisible((prev) => !prev);
        }}
      >
        <div
          className={`pointer-events-none absolute bottom-full z-10 mb-2 w-max rounded-md bg-gray-900 px-3 py-1.5 text-xs font-medium whitespace-nowrap text-white opacity-0 transition-opacity group-hover:opacity-100 dark:bg-gray-700 ${isTooltipVisible ? "!opacity-100" : ""}`}
        >
          <h4 className="mb-1 text-center font-bold">{provider} Accounts</h4>
          <ul>
            {Object.entries(statusCounts).map(([status, count]) => (
              <li key={status} className="flex items-center space-x-2">
                <span
                  className={`h-2 w-2 rounded-full ${getStatusDotStyle(status)}`}
                ></span>
                <span>
                  {count}{" "}
                  {status.charAt(0).toUpperCase() +
                    status.slice(1).toLowerCase().replace(/[-_]/g, " ")}
                </span>
              </li>
            ))}
          </ul>
          <div className="absolute top-full left-1/2 -translate-x-1/2 border-4 border-x-transparent border-t-gray-900 border-b-transparent dark:border-t-gray-700"></div>
        </div>
        {provider === "AZURE" ? (
          <VscAzure className="mr-2 h-8 w-8 text-[#0078D4]" title="Azure" />
        ) : (
          <FaAws className="mr-2 h-8 w-8 text-[#FF9900]" title="AWS" />
        )}
        <div className="mt-2 flex flex-row space-x-2">
          {Object.keys(statusCounts).map((status) => (
            <div
              key={status}
              className={`h-2 w-2 rounded-full ${getStatusDotStyle(status)}`}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default function EngagementItem({
  id,
  clientName,
  title,
  users,
  status,
  engagement,
}: EngagementItemProps) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const [formData, setFormData] = useState({
    client_name: "",
    wbs_code: "",
    title: "",
    users: [] as EngagementUser[],
  });

  const { data: availableUsers } = useGetUsers({
    query: {
      queryKey: getGetUsersQueryKey(),
      enabled: isEditModalOpen,
    },
  });

  const allAvailableUsers = availableUsers?.users || [];

  useEffect(() => {
    if (engagement) {
      const { client_name, wbs_code, title, users } = engagement;
      setFormData({
        client_name: client_name || "",
        wbs_code: wbs_code || "",
        title: title || "",
        users: users || [],
      });
    }
  }, [engagement, isEditModalOpen]);

  const editMutation = useEditEngagement({
    mutation: {
      onSuccess: () => {
        toast.success("Engagement successfully updated.");
        queryClient.invalidateQueries({
          queryKey: getGetEngagementsQueryKey(),
        });
      },
      onError: (error) => {
        toast.error(`Failed to update engagement: ${error.message}`);
      },
      onSettled: () => {
        setIsEditModalOpen(false);
      },
    },
  });

  const handleEditSubmit = () => {
    editMutation.mutate({
      engagementID: id,
      data: {
        wbs_code: formData.wbs_code,
        title: formData.title,
        user_ids: formData.users.map((user) => user.id),
      },
    });
  };

  const handleMultiSelectChange = (e: MultiSelectChangeEvent) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  return (
    <div className="relative">
      <Link
        to="/engagements/$engagementId"
        params={{
          engagementId: id,
        }}
        activeProps={{ className: "text-black font-bold" }}
        className="block text-blue-800 hover:text-blue-600"
      >
        <div className="flex flex-col rounded-lg bg-white shadow-md md:flex-row dark:divide-none dark:bg-[#374357b5]">
          <div className="flex basis-1/3 flex-row-reverse content-center justify-between space-y-1 space-x-5 p-5 md:flex-row md:items-center md:justify-start">
            <div className="content-center md:basis-1/2 lg:basis-1/4">
              <span
                className={`flex w-full flex-row content-center items-center rounded-full px-3 py-1 shadow-md ${getStatusColor(
                  status,
                )}`}
              >
                {getStatusIcon(status)}
                {(status ?? "")
                  .toLowerCase()
                  .replace(/[-_]/g, " ")
                  .replace(/\b\w/g, (char: string) => char.toUpperCase())}
              </span>
            </div>
            <div className="flex flex-col">
              <span className="text-xs font-normal text-slate-400 uppercase">
                {clientName}
              </span>
              <span className="text-base font-semibold text-slate-700 dark:text-white">
                {title}
              </span>
            </div>
          </div>

          <div className="hidden items-center justify-center md:flex md:flex-col">
            <div className="h-12 w-px bg-gray-200 dark:bg-gray-700"></div>
          </div>

          <div className="flex basis-1/3 flex-col space-y-1 p-5 md:items-end">
            <div className="text-xs font-normal text-slate-400">
              TEAM ({users?.length})
            </div>
            <div className="flex flex-row space-x-2">
              {users?.map((user, index) => (
                <div
                  key={index}
                  className="flex h-8 w-8 items-center justify-center rounded-full bg-lime-500 text-sm font-medium text-white"
                >
                  {user.full_name
                    .split(" ")
                    .map((word) => word.charAt(0))
                    .join("")}
                </div>
              ))}
            </div>
          </div>

          <div className="hidden items-center justify-center md:flex md:flex-col">
            <div className="h-12 w-px bg-gray-200 dark:bg-gray-700"></div>
          </div>

          <div className="flex basis-1/3 flex-row items-center justify-between space-x-4 p-5 md:justify-end">
            <div className="flex flex-row space-x-4">
              <ProviderStatusIndicator engagementId={id} provider="AZURE" />
              <ProviderStatusIndicator engagementId={id} provider="AWS" />
            </div>
            <div
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <Menu as="div" className="relative inline-block text-left">
                <MenuButton className="cursor-pointer rounded-full p-2 hover:bg-gray-100 focus:outline-none dark:hover:bg-gray-700">
                  <HiEllipsisVertical className="h-8 w-8 text-black dark:text-white" />
                </MenuButton>
                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <MenuItems className="ring-opacity-5 absolute right-0 z-50 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-gray-400 focus:outline-none dark:bg-gray-800">
                    <div className="px-1 py-1">
                      <MenuItem>
                        {({ active }) => (
                          <button
                            onClick={() => {
                              navigate({
                                to: "/engagements/$engagementId",
                                params: { engagementId: id },
                              });
                            }}
                            className={`${
                              active
                                ? "cursor-pointer bg-gray-100 text-gray-900"
                                : "text-gray-900 dark:text-white"
                            } group flex w-full items-center rounded-md px-2 py-2 text-sm`}
                          >
                            View details
                          </button>
                        )}
                      </MenuItem>
                      <MenuItem>
                        {({ active }) => (
                          <button
                            onClick={() => {
                              setIsEditModalOpen(true);
                            }}
                            className={`${
                              active
                                ? "cursor-pointer bg-gray-100 text-gray-900"
                                : "text-gray-900 dark:text-white"
                            } group flex w-full items-center rounded-md px-2 py-2 text-sm`}
                          >
                            Edit
                          </button>
                        )}
                      </MenuItem>
                    </div>
                  </MenuItems>
                </Transition>
              </Menu>
            </div>
          </div>
        </div>
      </Link>

      {isEditModalOpen && (
        <EditEngagementModal
          engagementID={id}
          title={formData.title}
          wbsCode={formData.wbs_code}
          clientName={formData.client_name}
          users={formData.users}
          availableUsers={allAvailableUsers}
          handleSubmit={() => handleEditSubmit()}
          isOpen={isEditModalOpen}
          closeModal={() => setIsEditModalOpen(false)}
          handleInputChange={handleInputChange}
          handleMultiSelectChange={handleMultiSelectChange}
        />
      )}
    </div>
  );
}
