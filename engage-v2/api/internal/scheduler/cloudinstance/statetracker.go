package cloudinstance

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/netip"
	"time"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/jackc/pgx/v5/pgtype"
	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments/keys"
)

// CloudInstanceStateCheckTask represents a task to check and log cloud instance state changes
type CloudInstanceStateCheckTask struct {
	NodeID        string `json:"node_id"`
	InstanceID    string `json:"instance_id"`
	Action        string `json:"action"`
	UserID        string `json:"user_id"`
	OldIP         string `json:"old_ip,omitempty"`
	ExpectedState string `json:"expected_state"`
	Provider      string `json:"provider"` // AWS, AZURE, etc.
	Region        string `json:"region"`
	AccountID     string `json:"account_id"`
	Timestamp     int64  `json:"timestamp"`
}

// PublishStateCheckTask publishes a cloud instance state check task to RabbitMQ
func PublishStateCheckTask(ctx context.Context, ch *amqp.Channel, queueName string, task CloudInstanceStateCheckTask) error {
	task.Timestamp = time.Now().Unix()

	body, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to marshal cloud instance state check task: %w", err)
	}

	// Debug log for outgoing message
	slog.Info("Publishing state-check task",
		"queue", queueName,
		"provider", task.Provider,
		"account_id", task.AccountID,
		"region", task.Region,
		"instance_id", task.InstanceID,
		"node_id", task.NodeID,
		"expected_state", task.ExpectedState,
		"action", task.Action,
	)

	err = ch.PublishWithContext(ctx,
		"",        // exchange
		queueName, // routing key (queue name)
		false,     // mandatory
		false,     // immediate
		amqp.Publishing{
			ContentType: "application/json",
			Body:        body,
		},
	)
	if err != nil {
		return fmt.Errorf("failed to publish cloud instance state check task: %w", err)
	}
	return nil
}

// StartStateCheckWorker starts a RabbitMQ consumer that processes cloud instance state check tasks
func StartStateCheckWorker(ch *amqp.Channel, queueName string, queries *db.Queries, logger *slog.Logger) {
	q, err := ch.QueueDeclare(
		queueName, // name
		true,      // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // no-wait
		nil,       // arguments
	)
	if err != nil {
		logger.Error("Failed to declare cloud instance state check queue", "error", err)
		return
	}

	msgs, err := ch.Consume(
		q.Name, // queue
		"",     // consumer
		false,  // auto-ack - we want to ack after processing
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		logger.Error("Failed to register cloud instance state check consumer", "error", err)
		return
	}

	go func() {
		for msg := range msgs {
			var task CloudInstanceStateCheckTask
			if err := json.Unmarshal(msg.Body, &task); err != nil {
				logger.Error("Failed to unmarshal state check task", "error", err)
				if nackErr := msg.Nack(false, false); nackErr != nil {
					logger.Error("Failed to nack message", "error", nackErr)
				}
				continue
			}

			logger.Info("Processing cloud instance state check task",
				"node_id", task.NodeID,
				"instance_id", task.InstanceID,
				"action", task.Action)

			err := ProcessStateCheckTask(queries, task, logger)
			if err != nil {
				logger.Error("Error processing cloud instance state check task",
					"node_id", task.NodeID,
					"error", err)
				// Requeue the message for retry (with exponential backoff handled by RabbitMQ)
				if nackErr := msg.Nack(false, true); nackErr != nil {
					logger.Error("Failed to nack message", "error", nackErr)
				}
			} else {
				if ackErr := msg.Ack(false); ackErr != nil {
					logger.Error("Failed to ack message", "error", ackErr)
				}
			}
		}
	}()
}

// ProcessStateCheckTask processes a single state check task
func ProcessStateCheckTask(queries *db.Queries, task CloudInstanceStateCheckTask, logger *slog.Logger) error {
	ctx := context.Background()

	// Add delay to allow AWS state to stabilize
	time.Sleep(30 * time.Second)

	switch task.Provider {
	case "AWS":
		return processAWSStateCheck(queries, task, logger, ctx)
	case "AZURE":
		return processAzureStateCheck(queries, task, logger, ctx)
	default:
		return fmt.Errorf("unsupported provider: %s", task.Provider)
	}
}

// processAWSStateCheck handles AWS-specific state checking
func processAWSStateCheck(queries *db.Queries, task CloudInstanceStateCheckTask, logger *slog.Logger, ctx context.Context) error {
	// Get AWS client (you'll need to implement this based on your existing AWS client setup)
	client, err := getAWSClientForAccount(task.AccountID, task.Region)
	if err != nil {
		return fmt.Errorf("failed to get AWS client: %w", err)
	}

	// Get actual instance state and IP from AWS
	resp, err := client.DescribeInstances(ctx, &ec2.DescribeInstancesInput{
		InstanceIds: []string{task.InstanceID},
	})
	if err != nil {
		return fmt.Errorf("failed to describe instance: %w", err)
	}

	if len(resp.Reservations) == 0 || len(resp.Reservations[0].Instances) == 0 {
		return fmt.Errorf("instance not found: %s", task.InstanceID)
	}

	instance := resp.Reservations[0].Instances[0]
	actualState := string(instance.State.Name)
	var actualIP string
	if instance.PublicIpAddress != nil {
		actualIP = *instance.PublicIpAddress
	}

	// Update database with actual state
	nodeIDPg, err := converters.StringToPgTypeUUID(task.NodeID)
	if err != nil {
		return fmt.Errorf("failed to convert node ID: %w", err)
	}

	// Update cloud instance state
	err = queries.UpdateCloudInstanceAWSState(ctx, db.UpdateCloudInstanceAWSStateParams{
		CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(actualState), Valid: true},
		CloudInstanceID:    pgtype.Text{String: task.InstanceID, Valid: true},
	})
	if err != nil {
		logger.Error("Failed to update cloud instance state", "error", err)
	}

	// Update IP address if it changed, guarded by node_id + instance_id
	if actualIP != "" {
		if addr, err := netip.ParseAddr(actualIP); err == nil {
			if rows, uerr := queries.UpdateCloudInstanceIPByNodeAndID(ctx, db.UpdateCloudInstanceIPByNodeAndIDParams{
				PublicIpv4Address: &addr,
				NodeID:            *nodeIDPg,
				CloudInstanceID:   pgtype.Text{String: task.InstanceID, Valid: true},
			}); uerr != nil {
				logger.Error("Failed to update IP address (guarded)", "error", uerr)
			} else if rows == 0 {
				logger.Warn("No row matched for guarded IP update", "node_id", task.NodeID, "instance_id", task.InstanceID, "ip", actualIP)
			}
		}
	}

	// Log IP changes with accurate information
	return logIPChange(queries, task, actualIP, logger, ctx)
}

// processAzureStateCheck handles Azure-specific state checking
func processAzureStateCheck(_ *db.Queries, task CloudInstanceStateCheckTask, logger *slog.Logger, _ context.Context) error {
	// TODO: Implement Azure state checking similar to AWS
	logger.Info("Azure state checking not yet implemented", "task", task)
	return nil
}

// logIPChange logs IP address changes with accurate state information
func logIPChange(queries *db.Queries, task CloudInstanceStateCheckTask, actualIP string, logger *slog.Logger, ctx context.Context) error {
	userIDPg, err := converters.StringToPgTypeUUID(task.UserID)
	if err != nil {
		return fmt.Errorf("failed to convert user ID: %w", err)
	}

	nodeIDPg, err := converters.StringToPgTypeUUID(task.NodeID)
	if err != nil {
		return fmt.Errorf("failed to convert node ID: %w", err)
	}

	timestamp := pgtype.Timestamp{
		Time:  time.Now(),
		Valid: true,
	}

	var logMessage string
	switch task.Action {
	case "reapi":
		if task.OldIP != "" && actualIP != "" && task.OldIP != actualIP {
			logMessage = fmt.Sprintf("AWS instance REAPI completed (%s): IP changed from %s to %s", task.InstanceID, task.OldIP, actualIP)
		} else if actualIP != "" {
			logMessage = fmt.Sprintf("AWS instance REAPI completed (%s): IP confirmed as %s", task.InstanceID, actualIP)
		} else {
			logMessage = fmt.Sprintf("AWS instance REAPI completed (%s): no IP assigned", task.InstanceID)
		}
	case "start":
		if actualIP != "" {
			logMessage = fmt.Sprintf("AWS instance start completed (%s): IP assigned %s", task.InstanceID, actualIP)
		} else {
			logMessage = fmt.Sprintf("AWS instance start completed (%s): no IP assigned", task.InstanceID)
		}
	default:
		if actualIP != "" {
			logMessage = fmt.Sprintf("AWS instance %s completed (%s): final IP %s", task.Action, task.InstanceID, actualIP)
		} else {
			logMessage = fmt.Sprintf("AWS instance %s completed (%s)", task.Action, task.InstanceID)
		}
	}

	err = queries.InsertActivityLog(ctx, db.InsertActivityLogParams{
		Message:   logMessage,
		Type:      db.LogsNodesTypeEnumNODEUPDATE,
		UserID:    *userIDPg,
		NodeID:    *nodeIDPg,
		CreatedAt: timestamp,
	})
	if err != nil {
		return fmt.Errorf("failed to insert activity log: %w", err)
	}

	logger.Info("Logged IP change",
		"node_id", task.NodeID,
		"action", task.Action,
		"old_ip", task.OldIP,
		"new_ip", actualIP)

	return nil
}

// getAWSClientForAccount creates an AWS EC2 client for the given account and region
func getAWSClientForAccount(accountID, region string) (*ec2.Client, error) {
	// Create a new SecretsManager instance (using us-east-1 as default region for secrets)
	sm, err := keys.NewSecretsManager("us-east-1")
	if err != nil {
		return nil, fmt.Errorf("failed to create secrets manager: %w", err)
	}

	// Get credentials from secrets manager
	secretMap, err := sm.GetSecret(accountID)
	if err != nil {
		return nil, fmt.Errorf("failed to get secret for account %s: %w", accountID, err)
	}

	// Extract AWS credentials
	accessKey, ok := secretMap["access_key_id"]
	if !ok {
		return nil, fmt.Errorf("access_key_id not found in secret for account %s", accountID)
	}

	secretKey, ok := secretMap["access_key_secret"]
	if !ok {
		return nil, fmt.Errorf("access_key_secret not found in secret for account %s", accountID)
	}

	// Create AWS config with credentials
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKey, secretKey, "")),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	return ec2.NewFromConfig(cfg), nil
}
