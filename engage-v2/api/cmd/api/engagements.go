package main

import (
	"context"
	"net/http"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/engagements"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/users"
)

// queries.go (or in your queries package)

// addEngagementRoutes registers all /engagement routes to the application router
func addEngagementRoutes(api huma.API, a *application) {

	type CreateEngagementInput struct {
		Body struct {
			ClientName      string   `json:"client_name"`
			WbsCode         string   `json:"wbs_code" example:"wbs0001"`
			EngagementTitle string   `json:"engagement_title" maxLength:"250"`
			Usernames       []string `json:"usernames"`
		}
	}

	// Create Engagement
	huma.Register(api, huma.Operation{
		OperationID:   "post-create-engagement",
		Method:        http.MethodPost,
		Path:          "/engagement",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Create Engagement",
		Tags:          []string{"Engagements"},
		DefaultStatus: http.StatusCreated,
	}, func(ctx context.Context, i *CreateEngagementInput) (*struct{}, error) {
		createdBy, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		err := engagements.CreateEngagement(
			a.logger,
			a.secretKey,
			a.dbConn,
			a.queries,
			a.rabbitMqManager,
			a.terraformExecPath,
			i.Body.ClientName,
			i.Body.WbsCode,
			i.Body.EngagementTitle,
			i.Body.Usernames,
			a.awsRootEmail,
			a.awsRootRegion,
			createdBy)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		return nil, nil
	})

	// Get Engagement
	type GetEngagementInput struct {
		EngagementID      string `path:"engagementID" format:"uuid" doc:"Engagement ID" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
		IncludeNodeGroups bool   `query:"node_groups" format:"bool" doc:"Whether to include Node Groups (optional). Example: true or false." default:"true"`
	}

	type GetEngagementOutput struct {
		Body struct {
			Engagement engagements.Engagement `json:"engagement"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-engagement",
		Method:        http.MethodGet,
		Path:          "/engagements/{engagementID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Engagement",
		Tags:          []string{"Engagements"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *GetEngagementInput) (*GetEngagementOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp := &GetEngagementOutput{}
		engagementResult, err := engagements.GetEngagement(a.queries, input.EngagementID, userID, input.IncludeNodeGroups)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		if engagementResult == nil {
			return nil, huma.Error404NotFound("Engagement not found")
		}
		resp.Body.Engagement = *engagementResult

		return resp, nil
	})

	type GetEngagementCloudInstancesOutput struct {
		Body struct {
			CloudInstanceNodeGroups []engagements.CloudInstanceNodeGroups `json:"cloud_instance_node_groups"`
		}
	}

	// Edit Engagement
	type EditEngagementInput struct {
		EngagementID string `path:"engagementID" format:"uuid" doc:"Engagement ID" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
		Body         struct {
			Title           string   `json:"title"`
			WbsCode         string   `json:"wbs_code"`
			AssignedUserIDs []string `json:"user_ids" format:"uuid" doc:"User IDs"`
		}
	}

	type EditEngagementOutput struct {
		Body struct {
			Engagement engagements.Engagement `json:"engagement"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "edit-engagement",
		Method:        http.MethodPut,
		Path:          "/engagements/{engagementID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Edit an Engagement",
		Tags:          []string{"Engagements"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *EditEngagementInput) (*EditEngagementOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		engagementIDPgType, err := converters.StringToPgTypeUUID(i.EngagementID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp := &EditEngagementOutput{}

		err = a.queries.EditEngagement(context.Background(), db.EditEngagementParams{
			Title:   i.Body.Title,
			WbsCode: i.Body.WbsCode,
			UpdatedAt: pgtype.Timestamp{
				Time:             time.Now(),
				InfinityModifier: 0,
				Valid:            true,
			},
			ID: *engagementIDPgType,
		})
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		err = users.AssignUsersToEngagement(a.awsRootRegion, a.secretKey, a.logger, a.queries, i.EngagementID, i.Body.AssignedUserIDs)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		engagement, err := engagements.GetEngagement(a.queries, i.EngagementID, userID, false)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp.Body.Engagement = *engagement

		return resp, nil
	})

	// Deactivate engagement
	type DeactivateEngagementInput struct {
        EngagementID string `path:"engagementID" format:"uuid" doc:"Engagement ID" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
        Body         struct {
            IsActive bool `json:"is_active"`
        }
    }

    huma.Register(api, huma.Operation{
        OperationID:   "deactivate-engagement",
        Method:        http.MethodPut,
        Path:          "/engagements/{engagementID}/status",
        Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
        Summary:       "Update Engagement Status",
        Description:   "Updates engagement status by setting is_active field. Setting to false deactivates the engagement.",
        Tags:          []string{"Engagements"},
        DefaultStatus: http.StatusOK,
    }, func(ctx context.Context, input *DeactivateEngagementInput) (*struct{}, error) {
        userID, ok := ctx.Value("userID").(string)
        if !ok {
            return nil, huma.Error500InternalServerError("Something went wrong")
        }
		_ = userID // Use userID if needed for logging or further checks

        engagementIDPgType, err := converters.StringToPgTypeUUID(input.EngagementID)
        if err != nil {
            return nil, huma.Error400BadRequest("Invalid engagement ID")
        }

        if !input.Body.IsActive {
            err = a.queries.DeleteEngagement(ctx, *engagementIDPgType)
            if err != nil {
                return nil, huma.Error500InternalServerError("Failed to deactivate engagement")
            }
        } else {
            err = a.queries.RestoreEngagement(ctx, *engagementIDPgType)
            if err != nil {
                return nil, huma.Error500InternalServerError("Failed to restore engagement")
            }
        }

        return nil, nil
    })

	// Get Engagement Cloud Instances
	huma.Register(api, huma.Operation{
		OperationID:   "get-engagement-cloud-instances",
		Method:        http.MethodGet,
		Path:          "/engagements/{engagementID}/cloud-instances",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Engagement Cloud Instances",
		Tags:          []string{"Engagements, Cloud Instances"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct {
		EngagementID string `path:"engagementID" format:"uuid" doc:"Engagement ID" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
	}) (*GetEngagementCloudInstancesOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp := &GetEngagementCloudInstancesOutput{}
		engagementIDs := []string{input.EngagementID}
		cloudInstanceNodeGroups, err := engagements.GetEngagementCloudInstances(a.queries, engagementIDs, userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp.Body.CloudInstanceNodeGroups = cloudInstanceNodeGroups

		return resp, nil
	})

	type GetEngagementsOutput struct {
		Body struct {
			Engagements []engagements.Engagement `json:"engagements"`
		}
	}

	// Get Engagements
	huma.Register(api, huma.Operation{
		OperationID:   "get-engagements",
		Method:        http.MethodGet,
		Path:          "/engagements",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Engagements",
		Tags:          []string{"Engagements"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetEngagementsOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		roles, ok := ctx.Value("roles").([]string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp := &GetEngagementsOutput{}

		// Retrieve engagements from the DB (which are updated asynchronously).
		engagementsResults, err := engagements.GetEngagements(a.queries, userID, roles)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp.Body.Engagements = engagementsResults
		return resp, nil
	})

	type GetEngagementGraphsOutput struct {
		Body struct {
			Graphs []engagements.Graph `json:"graphs"`
		}
	}

	// Get Engagement Graphs
	huma.Register(api, huma.Operation{
		OperationID:   "get-engagement-graphs",
		Method:        http.MethodGet,
		Path:          "/engagements/{engagementID}/graphs",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Engagement Graphs",
		Tags:          []string{"Engagements, Graphs"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct {
		EngagementID string `path:"engagementID" format:"uuid" doc:"Engagement ID" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
	}) (*GetEngagementGraphsOutput, error) {
		// Log the start of the request with engagement ID
		a.logger.Info("GET /engagements/{engagementID}/graphs request started",
			"engagementID", input.EngagementID)

		userID, ok := ctx.Value("userID").(string)
		if !ok {
			a.logger.Error("Failed to get userID from context",
				"engagementID", input.EngagementID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		a.logger.Info("Processing graphs request",
			"engagementID", input.EngagementID,
			"userID", userID)

		resp := &GetEngagementGraphsOutput{}

		// Log before database call
		a.logger.Info("Calling GetEngagementGraphNodes",
			"engagementID", input.EngagementID,
			"userID", userID)

		engagementGraphs, err := engagements.GetEngagementGraphNodes(a.queries, input.EngagementID, userID)

		// Log after database call
		if err != nil {
			a.logger.Error("GetEngagementGraphNodes failed",
				"engagementID", input.EngagementID,
				"userID", userID,
				"error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		a.logger.Info("GetEngagementGraphNodes succeeded",
			"engagementID", input.EngagementID,
			"userID", userID,
			"graphCount", len(engagementGraphs))

		resp.Body.Graphs = engagementGraphs

		// Log successful completion
		a.logger.Info("GET /engagements/{engagementID}/graphs request completed successfully",
			"engagementID", input.EngagementID,
			"graphCount", len(engagementGraphs))

		return resp, nil
	})

	type GetEngagementGraphOutput struct {
		Body struct {
			Graph engagements.Graph `json:"graph"`
		}
	}

	// Get Engagement Graph
	huma.Register(api, huma.Operation{
		OperationID:   "get-engagement-graph",
		Method:        http.MethodGet,
		Path:          "/engagements/{engagementID}/graphs/{nodeGroupID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get an Engagement Graph given its Node Group ID",
		Tags:          []string{"Engagements, Graphs"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct {
		EngagementID string `path:"engagementID" format:"uuid" doc:"Engagement ID" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
		NodeGroupID  string `path:"nodeGroupID" format:"uuid" doc:"Node Group ID" example:"9a7e965f-43ed-434b-bf08-059e8dca0111"`
	}) (*GetEngagementGraphOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp := &GetEngagementGraphOutput{}
		engagementGraph, err := engagements.GetEngagementGraphNode(a.queries, input.EngagementID, input.NodeGroupID, userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp.Body.Graph = *engagementGraph
		return resp, nil
	})

	type CloudAccount struct {
		ID          string `json:"id"`           // internal DB UUID
		DisplayName string `json:"display_name"` // nickname (AWS) or tenant_id (Azure)
		Provider    string `json:"provider"`
	}

	type GetCloudAccountsOutput struct {
		Body struct {
			Accounts []CloudAccount `json:"accounts"`
		}
	}
	// get all accounts and tenants that are usable for deploying cloud instaces
	huma.Register(api, huma.Operation{
		OperationID:   "get-engagement-cloud-accounts",
		Method:        http.MethodGet,
		Path:          "/engagements/{engagementID}/cloud-accounts",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get all cloud accounts (AWS + Azure) for an engagement",
		Tags:          []string{"Engagements", "Cloud Accounts"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct {
		EngagementID string `path:"engagementID" format:"uuid"`
	}) (*GetCloudAccountsOutput, error) {

		engagementID, err := converters.StringToPgTypeUUID(input.EngagementID)
		if err != nil {
			return nil, huma.Error400BadRequest("Invalid engagement ID")
		}

		// AWS Accounts
		awsAccounts, err := a.queries.ListAWSAccountsByEngagementIDAndStatus(ctx, db.ListAWSAccountsByEngagementIDAndStatusParams{
			EngagementID:          *engagementID,
			AccountCreationStatus: pgtype.Text{String: "SUCCESS", Valid: true},
			AccountCloudStatus:    pgtype.Text{String: "ACTIVE", Valid: true},
		})
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to fetch AWS accounts")
		}

		// Azure Tenants
		azureTenants, err := a.queries.ListAzureTenantsByEngagementIDAndStatus(ctx, db.ListAzureTenantsByEngagementIDAndStatusParams{
			EngagementID:       *engagementID,
			AccountCloudStatus: pgtype.Text{String: "Enabled", Valid: true},
			SecretsSaved:       true,
			CreationStatus:     pgtype.Text{String: "SUCCESS", Valid: true},
		})

		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to fetch Azure tenants")
		}

		// Clouds response combined in DTO
		var accounts []CloudAccount

		for _, acc := range awsAccounts {
			accounts = append(accounts, CloudAccount{
				ID:          acc.ID.String(),
				DisplayName: acc.Nickname,
				Provider:    "AWS",
			})
		}

		for _, t := range azureTenants {
			accounts = append(accounts, CloudAccount{
				ID:          t.ID.String(),
				DisplayName: t.TenantID,
				Provider:    "AZURE",
			})
		}

		output := &GetCloudAccountsOutput{}
		output.Body.Accounts = accounts
		return output, nil
	})

}
