# EngageV2 Deployment Guide

## Overview

EngageV2 is a web application designed to assist with deploying repeatable, deniable offensive infrastructure for Simulated Attack engagement. It utilises Terraform to deploy the infrastructure but provides wrap-around deployment assistant, metrics and auditing controls. EngageV2 itself is designed to be deployed and hosted in AWS infrastructure with a focus on security due to the sensitive nature of the platform and the infrastructure it deploys. Due to the use of Terraform; deployed infrastructure can potentially be any vendor that is supported under Terraform (though limits may apply). This README provides an in-depth look at the deployment architecture and key considerations.

## Core Deployment Architecture

### Architecture Diagram

![EngageV2 Application Architecture](./architecture/images/structurizr-1-Deployment-001.svg)

The architecture diagram provides a visual representation of the EngageV2 application's infrastructure, showcasing the key components and their interactions. This will be updated as the solution matures.

### Infrastructure Components

**Warning:** This is intended to be deployed to a private Aviatrix VPC with public DNS resolving to private addresses. Some ISP routers will refuse to return DNS queries against public FQDNs which answer with private IP addresses. This is a known issue and the only workaround currently is to either disable DNS rebinding protection on your router or get a colleague to lookup the IPs and update your local system's DNS settings. 

The EngageV2 application is deployed using a multi-service architecture with the following key components:

1. **Frontend (UI)**
   - Hosted on Amazon S3
   - Distributed via Amazon CloudFront
   - TLS Certificate managed with ACM (AWS Certificate Manager)

2. **Backend (API)**
   - Deployed on AWS Elastic Beanstalk
   - Accessed via Amazon Application Load Balancer
   - Scales automatically via AWS Auto Scaling Groups
   - Running in a Docker container
   - Intended to be hosted in private Aviatrix subnets for restricted access
   - TLS Certificate managed with ACM (AWS Certificate Manager)

4. **Database**
   - PostgreSQL RDS instance
   - Intended to be hosted in private Aviatrix subnets for restricted access
   - Encrypted at rest
   - Automatic 7-day backup retention

5. **Message Queue**
   - RabbitMQ broker
   - Deployed via Amazon MQ
   - Intended to be hosted in private Aviatrix subnets for restricted access
   - Configured with secure communication protocols

### Account Components

The EngageV2 application touches across many many accounts for various purposes so for the benefit of consistency, these are mentioned here and terminology will remain consistent across the rest of the documentation:

#### AWS Parent Account
This is the parent account used for the Simulated Attack team to centralise billing of child accounts. This account is long-lived, should always have good standing and is host to the team's specific instance of EngageV2. Aka - this account is where this project is deployed **per team**

#### AWS Child/Engagement Accounts
Using AWS Organisations; child accounts are created for each live engagement (EngageV2 takes care of this). Each child account is used to deploy infrastructure associated with that engagement and only that engagement. **NB:** While EngageV2 is currently limited to AWS - this also applies to other vendors such as Azure.

#### CDI Production Account

This is a Microsoft Azure tenant operated by the CDI team and provide a centralised authentication and authorisation realm. No other resources are utilised from this account.

## Deployment Methodology

### TLDR;

1. Gather Pre-reqs:
   1. Provision required [AWS Parent Account](#aws-parent-account)
   2. Acquire required [CDI Production Account](#cdi-production-account) variables
   3. Determine R53 hosting naming and ensure AWS credentials can write to associated hosted zone in the [AWS Parent Account](#aws-parent-account)
   4. Acquire or create required GitLab credentials
2. Configure Scalr Workspace with this project as the source and collected variables
3. Deploy
4. ...
5. Profit. 

### Automated Infrastructure Provisioning

Once the pre-reqs have been acquired, the deployment is fully automated using Terraform, with key features:

- Dynamic resource naming using random strings - no hardcoded usernames/passwords/table names etc
- Secure secret generation for databases, message queues, and application keys
- Environment-specific configuration management
- Comprehensive security group configurations

The deployment is designed (and encouraged) to be re-run as part of ongoing maintenance schedules as it enables the following automated changes:

- Automatically renew and distribute required TLS certificates
- Ensure deployed nodes are running latest AMIs for the specified Beanstalk solution stack

The following variables will likely need occasional renewal depending on specific durations:

- Azure API Client Secret (`SSO_AZURE_API_CLIENT_SECRET`)
- GitLab Access Token (`GITLAB_ACCESS_TOKEN`)
- GitLab Docker Authentication Token (`GITLAB_DOCKER_ACCESS_TOKEN`)
- AWS Parent Account Deployment Key (`AWS_SECRET_ACCESS_KEY`)

## Deployment Variables

The following key variables are required for each deployment:

### General Application Variables

- `SUBDOMAIN_NAME`: Primary subdomain for the application. The API prefixes `api.` to the FQDN
- `DOMAIN_NAME`: Base domain
- `VERSION_TAG`: Specific application version to deploy. Allows control over upgrades.

### [AWS Parent Account](#aws-parent-account) Variables

- `AWS_REGION`: Region the application is deployed to
- `AWS_ACCESS_KEY_ID` Instance Deployment Access Key
- `AWS_SECRET_ACCESS_KEY` Instance Deployment Secret
- `NETWORK_VPC_ID`: ID of the VPC connected to Aviatrix
- `NETWORK_SUBNET_1_ID` ID of one of the subnets in the above VPC
- `NETWORK_SUBNET_2_ID` ID of one of the subnets in the above VPC

### GitLab Variables

- `GITLAB_DOCKER_ACCESS_TOKEN`: A Docker authentication token for accessing GitLab's Container Registry
- `GITLAB_ACCESS_TOKEN`: A Project Access token to allow accessing GitLab Releases

### [CDI Production Account](#cdi-production-account) Variables

- `SSO_AZURE_TENANT_ID`: Tenant ID of the Azure Account responsible for authentication
- `ENGAGE_AZURE_STANDARD_USERS_GRP_ID`: Group ID of the "Standard Users" group in Azure. Responsible for determining who is a "Standard User"
- `ENGAGE_AZURE_ADMIN_USERS_GRP_ID`: Group ID of the "Admin Users" group in Azure. Responsible for determining who is an "Admin User"
- `SSO_AZURE_API_APP_ID`: API Application Client ID
- `SSO_AZURE_API_CLIENT_SECRET`: API Application Client Secret
- `UI_AZURE_APP_ID`: UI Application Client ID
- `UI_AZURE_API_SCOPE`: API Scope Name

Due to the necessity for segregation between instances, the only two variables likely to be shared across instances are `SSO_AZURE_TENANT_ID` and `UI_AZURE_API_SCOPE`.

## Deployment Workflow

### Code Release

Development of EngageV2 happens in a separate GitLab project ([Engage-V2](https://gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2)): 

As part of the development process, once a release is deemed ready, it is tagged in Git which triggers the release pipeline generating the following assets:

- Compiled static HTML files as GitLab Release Asset items
- Docker Image tagged with the release tag name.

The tag name is what we then need to determine what version the deployment is using.

### Pre-requirements Gathering

Initial deployment is easily the hardest part due to the requirement of many components prior to actually deploying any instance.

#### [AWS Parent Account](#aws-parent-account)

This guide will not cover how to setup a new [AWS Parent Account](#aws-parent-account) as that is beyond the scope of this readme. 

1. Create AWS Access Key/Secret with fairly permissive permissions (an IAM policy will be updated in this guide soon. For now; YOLO, give it Admin). This provides you with the **`AWS_ACCESS_KEY_ID`** and **`AWS_SECRET_ACCESS_KEY`** variables. Obviously chose your region too which gives you the **`AWS_REGION`** variable.
2. Create a Route 53 hosted zone for your chosen **`DOMAIN_NAME`**. It's beyond this guide if you want to register a new domain dedicated to your instance or piggy-back off an internal domain. Either way; the requirement is you need a Hosted Zone for `DOMAIN_NAME` which Terraform can create two `A` records of `{SUBDOMAIN_NAME}.{DOMAIN_NAME}` and `api.{SUBDOMAIN_NAME}.{DOMAIN_NAME}`.
   1. **`SUBDOMAIN_NAME`** can be what ever you want, but must not be blank.
3. Assuming the account has been onboarded to the Aviatrix solution, identify the Aviatrix VPC and it's private subnets and obtain the IDs for the following variables:
   1. **`NETWORK_VPC_ID`**
   2. **`NETWORK_SUBNET_1_ID`**
   3. **`NETWORK_SUBNET_2_ID`**

#### [CDI Production Account](#cdi-production-account)

This gets particularly funky! All of these actions must be performed by a CDI administrator due to the privileges required. Since the CDI Production account is an Azure account - these actions are all inside the Azure portal. 

3. Visit the [Entra Overview](https://portal.azure.com/#view/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/~/Overview) page and obtain the `Tenant ID` - this is the **`SSO_AZURE_TENANT_ID`** variable.
4. Visit [Entra Groups](https://portal.azure.com/#view/Microsoft_AAD_IAM/GroupsManagementMenuBlade/~/Overview):
   1. Create a group for admin users. A name like this is suggested: `EngageV2_{TEAM}_Admins`. Make a note of it's `Object ID`, this is the **`ENGAGE_AZURE_ADMIN_USERS_GRP_ID`** value.
   2. Create a group for standard users. A name like this is suggested: `EngageV2_{TEAM}_Users`. Make a note of it's `Object ID`, this is the **`ENGAGE_AZURE_STANDARD_USERS_GRP_ID`** value.
   3. Assign relevant users to relevant groups. As a recap - members of the admin group have full access over the deployed EngageV2 instance. Standard users only see engagements they are assigned to (managed in EngageV2).
5. Visit [Entra App Registrations](https://portal.azure.com/#view/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/~/RegisteredApps)
6. Create the UI App (select `New Registration`):
   1. Give it a name like `EngageV2_{TEAM}_UI` (aka `EngageV2_UKI_UI`)
   2. For supported account types, select `Accounts in this organizational directory only`
   3. Under Redirect URI, select `Single-page application (SPA)`
   4. Click Register
   5. On the Overview page, note the `Application (client) ID` value. This is the **`UI_AZURE_APP_ID`** value.
   6. Visit the `Authentication` page and select `Add a platform`:
      1. Select `Single-page application`
      2. Set `Redirect URIs` to the URI of the **frontend** . This will be `https://{SUBDOMAIN_NAME}.{DOMAIN_NAME}`
      3. Select the checkbox `Access tokens (used for implicit flows)` under the `Implicit grant and hybrid flows` section
      4. Save
   7. The UI App Registration is complete!
7. Create the API App (select `New Registration`):
   1. Give it a name like `EngageV2_{TEAM}_API` (aka `EngageV2_UKI_API`)
   2. For supported account types, select `Accounts in this organizational directory only`
   3. Under Redirect URI, select `Web`
   4. Click Register
   5. On the Overview page, note the `Application (client) ID` value. This is the **`SSO_AZURE_API_APP_ID`** value.
   6. Visit the `Expose an API` page
   7. Click `Add a scope` under the `Scopes defined by this API` section. 
      1. *The first time you do this, it will prompt you to set an application ID URI, accept the default value and click `Save and continue`*
      2. Set `Scope name` to the value `user_impersonation` *(this matches the **`UI_AZURE_API_SCOPE`** variable we set in the deployment. The value doesn't matter but both must match)*
      3. Set `Who can consent` to `Admins only`
      4. Set `Admin Consent Display Name` and `Admin consent description` to something useful describing Engage such as `EngageV2 API`
      5. Save!
   8. Visit `API permissions`
   9. Click `Add a permission` and add the following permissions:
      1. `Microsoft Graph` > `Application permissions` > `Group.Read.All`
      2. `Microsoft Graph` > `Application permissions` > `User.Read.All`
      3. `Microsoft Graph` > `Delegated permissions` > `User.Read`
   10. Visit `Certificates & secrets`
   11. Click `New client secret`. Create a new secret as per CDI guidelines for expiry etc. 
      1. Copy the `Value` for the secret just generated. This is the **`SSO_AZURE_API_CLIENT_SECRET`** value.
   12. Allow the API to embed user's groups in the JWT. Visit `Token Configuration`
      1. Select `Add groups claim` and select the group type `Security Groups`
      2. Save!
   13. Visit `App roles`
   14. Create the Admin app role:
       1.  Select `Create app role`
       2.  Set `Display name` to `Admin`
       3.  Set `Allowed member types` to `Users/Groups`
       4.  Set `Value` to `Engage.Admin` **This is important and MUST be correct**
       5.  Provide a description (e.g. `EngageV2 Admin`)
       6.  Enable the app role
       7.  Apply!
   15. Create the Standard User app role:
       1.  Select `Create app role`
       2.  Set `Display name` to `Standard`
       3.  Set `Allowed member types` to `Users/Groups`
       4.  Set `Value` to `Engage.Standard` **This is important and MUST be correct**
       5.  Provide a description (e.g. `EngageV2 Standard User`)
       6.  Enable the app role
       7.  Apply!
   16. Visit `Manifest`
       1.  Select `AAD Graph App Manifest`
       2.  Edit the `accessTokenAcceptedVersion` value and set it to `2`. 
       3.  It should read this: `	"accessTokenAcceptedVersion": 2,`
       4.  Save
8.  Visit [Entra Enterprise Applications](https://portal.azure.com/#view/Microsoft_AAD_IAM/StartboardApplicationsMenuBlade/~/AppAppsPreview/menuId~/null)
9.  Select the correct API application you created above (e.g. `EngageV2_{TEAM}_API`)
10. On the `Properties` page, set the following settings to `Yes`:
    1.  `Assignment Required`
    2.  `Visible to users?`
11. Permit user groups to login. Select `User and groups`. Repeat the following tasks for both the `EngageV2_{TEAM}_Admins` and `EngageV2_{TEAM}_Users` groups.
    1.  Select `Add user/group`
    2.  Select the applicable group
    3.  Assign the applicable role

#### GitLab

GitLab is a bit simpler, only two components are needed:

12. Obtain a [Project access token](https://gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/-/settings/access_tokens) from the **Development** project (`engage-v2`).
    1. Set the token name and expiry date to something sensible
    2. Set the role to developer
    3. Set the scopes required: `read_api`, `read_registry`
    4. Create! The returned value is the **`GITLAB_ACCESS_TOKEN`** value.
13. Object a Docker authentication token
    1.  Using the obtained `GITLAB_ACCESS_TOKEN` value above; run the following two **shell** commands**:
        1.  `PATOKEN={GITLAB_ACCESS_TOKEN}`
        2.  `echo "$PATOKEN" | docker login registry.cyberdefense.global -u engage-v2 --password-stdin`
    2.  This should result in `Login Succeeded`
    3.  Now we need to extract the Docker authentication token. 
        1.  If you have `jq` installed, run the following command: `cat ~/.docker/config.json  |jq -r '.auths."registry.cyberdefense.global".auth'`
        2.  Otherwise view the file `~/.docker/config.json` (or the equivilent on a non Linux OS) and obtain the `auth` value under `registry.cyberdefense.global`.
    4.  This value is the **`GITLAB_DOCKER_ACCESS_TOKEN`**

Finally; using the [Releases]([xx](https://gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/-/releases)) page of the EngageV2 project, select the release you want and use that name as your **`VERSION_TAG`** variable.

Now you have all the required variables (see [Deployment Variables](#deployment-variables) to make sure), we should now be ready to deploy!

### Manual Deployment

You can manually deploy this project easily by creating a `terraform.tfvars` file with the gathered variables. An example file is included in this project. Once completed; you simply follow a normal Terraform run:

```shell
terraform init
terraform plan
terraform apply
```

Updates/changes/maintenance are as simple as:

```shell
terraform plan
terraform apply
```

The obvious downside is the Terraform state file is stored on the machine you run Terraform. This might be preferable for demo or UAT systems!


### Scalr Deployment

By far the most recommended deployment approach for production instances is via the CDI Scalr instance. 

Deployment is simple:
1. Create a new Workspace in whatever environment is appropriate
   1. Give the workspace an approprite name
   2. Select the correct type (likely `Production`)
   3. Select `Gitlab` from the VCS provider
   4. Select `cyber-defense-infrastructure/engage/engage-deployment` under `Repository`
   5. Select `develop` under branch
   6. Select any other settings appropriate for your needs.
   7. Create!
2. Under `Variables`, create Terraform (not Shell) variables for every variable collected earlier. Take care to make sure sensitive values are marked as sensitive.
3. Queue a new run and cross your fingers!


### Maintenance Updates

Due to the repeatability of the Terraform code; doing maintenance updates is as simple as triggering a new run.

### Upgrading

When updating to a new version, ensure the `VERSION_TAG` variable is updated to the desired tag name and trigger a new run.

**WARNING**: Currently there is no automatic database migration process so any releases with database schema changes will need manual effort. This should be resolved in the near future. 
