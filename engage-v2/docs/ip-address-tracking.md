# IP Address Tracking in Cloud Instance Activity Timeline

## Overview

This document describes the implementation of IP address tracking for cloud instance actions in the activity timeline. The solution enhances existing activity log messages to include IP address information without requiring database schema changes or modifications to the shared ActivityTimeline component.

## Implementation Approach

### Option Selected: Enhanced Message Content

We chose to enhance the existing activity log messages rather than adding new database fields because:

1. **Maintains Compatibility**: The ActivityTimeline component is shared across all node types
2. **No Schema Changes**: Avoids database migrations and maintains backward compatibility  
3. **Follows Existing Patterns**: Consistent with current logging approach
4. **Simpler Implementation**: No conditional rendering logic needed in the UI

### Changes Made

#### 1. Cloud Instance Actions Enhanced

The following cloud instance actions now track IP address changes:

- **REAPI (Stop + Start + IP Refresh)**: Tracks IP address changes from old to new
- **Start**: Records previous IP address before starting
- **Stop**: Records current IP address before stopping  
- **Terminate**: Records final IP address before termination
- **Reboot**: Records current IP address during reboot

#### 2. Deployment Process Enhanced

- **Initial Deployment**: Logs IP address assignment when cloud instance is first deployed

#### 3. Message Format Examples

```
// IP address change during REAPI
"AWS instance REAPI requested (i-1234567): stop+start completed, IP changed from ************* to *************"

// IP assignment during deployment  
"Cloud instance deployed successfully, IP address assigned: ************* (Instance ID: i-1234567)"

// Action with current IP tracking
"AWS instance stop requested (i-1234567), current IP: *************"

// Fallback when no IP available
"AWS instance start requested (i-1234567)"
```

## Files Modified

### Backend Changes

1. **`engage-v2/api/cmd/api/nodeTypeCloudInstance.go`**
   - Enhanced REAPI action to compare old vs new IP addresses
   - Enhanced start action to track previous IP
   - Enhanced stop action to track current IP  
   - Enhanced terminate action to track final IP
   - Enhanced reboot action to track current IP

2. **`engage-v2/api/internal/deployments/queue.go`**
   - Added IP address logging during initial deployment
   - Logs successful deployment with assigned IP address

### Frontend Changes

No frontend changes were required since the ActivityTimeline component already displays the enhanced messages.

## Technical Details

### Current Implementation Issues

**Problem**: The current implementation has state synchronization issues:

1. **Immediate DB Query**: We query the database immediately, but AWS operations are asynchronous
2. **State Mismatch**: Database might show "pending" while AWS shows "running"
3. **Race Conditions**: REAPI waits for AWS state changes but queries DB which might not be updated
4. **Sync Delay**: Cloud instance sync happens every 60 minutes, causing long periods of inconsistent state

### Proposed Better Solution: RabbitMQ-Based State Tracking

Instead of immediate database queries, we should:

1. **Queue State Check Tasks**: Publish RabbitMQ messages to check state after actions
2. **Async State Verification**: Use background workers to verify actual AWS state
3. **Delayed IP Tracking**: Log IP changes only after confirming state transitions
4. **Real-time Sync**: Trigger immediate sync tasks for specific instances

#### Proposed Implementation

```go
// 1. Publish state check task after action
type CloudInstanceStateCheckTask struct {
    NodeID     string `json:"node_id"`
    InstanceID string `json:"instance_id"`
    Action     string `json:"action"`
    UserID     string `json:"user_id"`
    OldIP      string `json:"old_ip,omitempty"`
    ExpectedState string `json:"expected_state"`
}

// 2. Background worker processes state checks
func ProcessStateCheckTask(task CloudInstanceStateCheckTask) {
    // Get actual AWS state
    actualState, actualIP := getAWSInstanceState(task.InstanceID)

    // Update database with real state
    updateDatabaseState(task.NodeID, actualState, actualIP)

    // Log IP changes with accurate information
    if task.OldIP != actualIP {
        logIPChange(task.NodeID, task.Action, task.OldIP, actualIP)
    }
}
```

### Current IP Address Retrieval (Problematic)

```go
// This approach is problematic - queries stale database
currentNode, err := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{
    NodeID: *nodeIDPg,
    ID:     *userIDPg,
})
if err == nil && currentNode.PublicIpv4Address != nil {
    currentIP = currentNode.PublicIpv4Address.String()
}
```

### Activity Log Insertion

Enhanced messages are logged using the existing pattern:

```go
_ = activitylogs.InsertLog(a.queries, logMessage, db.LogsNodesTypeEnumNODEUPDATE, *userIDPg, *nodeIDPg, ts)
```

### Error Handling

- IP address retrieval failures are handled gracefully
- Actions proceed normally even if IP tracking fails
- Fallback messages are used when IP addresses are unavailable

## Benefits

1. **Complete IP History**: Users can track IP address changes throughout the cloud instance lifecycle
2. **No Breaking Changes**: Existing functionality remains unchanged
3. **Shared Component**: Works seamlessly with the existing ActivityTimeline component
4. **Backward Compatible**: No database migrations required
5. **Consistent UX**: Follows existing activity log patterns

## Implementation Status

### Current Status (Interim Solution)
- ✅ Enhanced activity log messages with IP information
- ✅ Deployment IP assignment logging
- ⚠️ **Known Issue**: Immediate database queries may show stale state
- ⚠️ **Known Issue**: REAPI action has race conditions with state sync

### Recommended Next Steps (Better Solution)

1. **Integrate RabbitMQ State Tracker**:
   ```bash
   # Add to main.go
   cloudInstanceCh, err := app.getChannel()
   cloudinstance.StartStateCheckWorker(cloudInstanceCh, "cloud-instance-state-check", app.queries, app.logger)
   ```

2. **Update Cloud Instance Actions**:
   - Replace immediate IP queries with async state check tasks
   - Queue state verification tasks after each action
   - Log accurate IP changes after state confirmation

3. **Add Missing Dependencies**:
   - Implement `getAWSClientForAccount` function
   - Add region and account ID to state check tasks
   - Integrate with existing AWS client setup

### Files to Modify for Full Implementation

1. **`engage-v2/api/cmd/api/main.go`**: Add state check worker
2. **`engage-v2/api/cmd/api/nodeTypeCloudInstance.go`**: Replace immediate queries with async tasks
3. **`engage-v2/api/internal/scheduler/cloudinstance/statetracker.go`**: Complete AWS client integration

## Future Enhancements

Potential future improvements could include:

1. **Real-time State Sync**: Trigger immediate sync for specific instances
2. **IP Change Notifications**: Alert users when IP addresses change
3. **IP History API**: Dedicated endpoint for IP address change history
4. **Azure Support**: Extend async state tracking to Azure cloud instances
5. **State Transition Webhooks**: Notify external systems of state changes

## Testing

### Current Implementation Testing
1. Performing cloud instance actions (start, stop, reboot, terminate, REAPI)
2. Checking the activity timeline in the cloud instance details modal
3. Verifying IP address information appears in log messages
4. Confirming deployment logs include IP assignment information

### Future Implementation Testing
1. Verify RabbitMQ state check tasks are queued after actions
2. Confirm accurate IP tracking after state transitions
3. Test state synchronization under various AWS conditions
4. Validate error handling for failed state checks
