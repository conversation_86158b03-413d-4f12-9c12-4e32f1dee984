/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { EditScriptInputBodyScriptType } from "./editScriptInputBodyScriptType";

export interface EditScriptInputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  /** Content of the script */
  content: string;
  /** Updated description of the script */
  description: string;
  /** Updated name of the script */
  name: string;
  /** Type of the script */
  script_type: EditScriptInputBodyScriptType;
}
