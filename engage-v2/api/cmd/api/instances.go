package main

import (
	"context"
	"net/http"

	"github.com/danielgtaylor/huma/v2"
	authz "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/handlers/authz"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/instances"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/aws"
)

// addProvidersRoutes registers all /providers routes to the application router
func addInstancessRoutes(api huma.API, a *application) {

	// Combined endpoint for all AWS Instance Types (standard and custom)
	huma.Register(api, huma.Operation{
		OperationID:   "get-aws-instance-types",
		Method:        http.MethodGet,
		Path:          "/security/set-instance-types/aws/instance-types/{region}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get AWS instance types for a region",
		Tags:          []string{"Security, Instance Types"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct {
		Region string `path:"region" maxLength:"20" example:"eu-west-2" doc:"AWS Region"`
		Type   string `query:"type" example:"custom_alpha" doc:"Instance category type (optional)"`
	}) (*instances.GetInstanceTypesOutput, error) {
		// Admin-only access
		if err := authz.RequireAdmin(ctx); err != nil {
			return nil, huma.Error401Unauthorized("Unauthorized")
		}

		resp := &instances.GetInstanceTypesOutput{}
		awsEc2Client := aws.NewEc2Client(input.Region)

		instanceTypesResult, err := awsEc2Client.GetAllCloudInstanceTypes()
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to get instance types")
		}

		// Pass the type parameter directly to validation
		validation, err := awsEc2Client.ValidateInstanceTypesInRegion(a.queries, input.Type)
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to validate instance types")
		}

		mappings, err := a.queries.GetAllInstanceSizeMappings(ctx)
		if err != nil {
			a.logger.Error("Failed to get instance mappings",
				"error", err,
			)
			return nil, huma.Error500InternalServerError("Failed to get instance mappings")
		}

		resp.Body.Mappings = mappings
		resp.Body.InstanceTypes = instanceTypesResult
		resp.Body.Validation = validation
		return resp, nil
	})

	type InstanceTypeMappingChange struct {
		Provider     string `json:"provider"`
		SizeAlias    string `json:"size_alias"`
		Priority     int    `json:"priority"`
		InstanceType string `json:"instance_type"`
		Action       string `json:"action"` // "add" or "delete"
	}

	type SetInstanceTypeRequest struct {
		Changes            []InstanceTypeMappingChange `json:"changes"`
		PrioritizedRegions []string                    `json:"prioritizedRegions"`
	}

	type SetInstanceTypeInput struct {
		Body SetInstanceTypeRequest
	}

	// Set Instance Type
	huma.Register(api, huma.Operation{
		OperationID:   "set-instance-type",
		Method:        http.MethodPost,
		Path:          "/security/set-instance-types",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Set instance types for multiple size and priority combinations",
		Tags:          []string{"Security, Instance Types"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *SetInstanceTypeInput) (*struct{}, error) {
		// Admin-only access
		if err := authz.RequireAdmin(ctx); err != nil {
			return nil, huma.Error401Unauthorized("Unauthorized")
		}
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error401Unauthorized("Unauthorized")
		}

		awsEc2Client := aws.NewEc2Client(a.awsRootRegion)

		// Convert instance type changes
		awsChanges := make([]aws.InstanceTypeMappingChange, len(i.Body.Changes))
		for idx, change := range i.Body.Changes {
			awsChanges[idx] = aws.InstanceTypeMappingChange{
				Provider:     change.Provider,
				SizeAlias:    change.SizeAlias,
				Priority:     change.Priority,
				InstanceType: change.InstanceType,
				Action:       change.Action,
			}
		}

		// Update instance types
		err := awsEc2Client.UpdateInstanceTypes(ctx, a.queries, awsChanges)
		if err != nil {
			a.logger.Error("Failed to update instance types",
				"error", err,
				"userID", userID,
			)
			return nil, huma.Error500InternalServerError("Failed to update instance types")
		}

		// Sync prioritized regions
		err = awsEc2Client.SyncPrioritizedRegions(ctx, a.queries, i.Body.PrioritizedRegions)
		if err != nil {
			a.logger.Error("Failed to sync prioritized regions",
				"error", err,
				"userID", userID,
			)
			return nil, huma.Error500InternalServerError("Failed to sync prioritized regions")
		}

		return &struct{}{}, nil
	})
}
