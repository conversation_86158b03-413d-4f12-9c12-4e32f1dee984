{"configuration": {}, "description": "Description", "documentation": {}, "id": 1, "lastModifiedDate": "2025-03-04T12:30:21Z", "model": {"deploymentNodes": [{"children": [{"children": [{"environment": "Production", "id": "14", "infrastructureNodes": [{"environment": "Production", "id": "15", "name": "S3 Bucket", "properties": {"structurizr.dsl.identifier": "frontend_s3"}, "relationships": [{"description": "Serves static content from", "destinationId": "17", "id": "31", "sourceId": "15", "tags": "Relationship"}], "tags": "Element,Infrastructure Node,Amazon Web Services - Simple Storage Service Bucket"}], "instances": "1", "name": "S3", "properties": {"structurizr.dsl.identifier": "e3c118c1-9e91-485e-94e6-f38ca74ea712"}, "tags": "Element,Deployment Node,Amazon Web Services - Simple Storage Service S3 Standard"}, {"children": [{"children": [{"children": [{"containerInstances": [{"containerId": "3", "deploymentGroups": ["<PERSON><PERSON><PERSON>"], "environment": "Production", "id": "21", "instanceId": 1, "properties": {"structurizr.dsl.identifier": "webapplicationinstance"}, "relationships": [{"description": "Reads from and writes to", "destinationId": "24", "id": "25", "linkedRelationshipId": "9", "sourceId": "21"}, {"description": "Reads from and writes to", "destinationId": "27", "id": "28", "linkedRelationshipId": "10", "sourceId": "21"}], "tags": "Container Instance,Amazon Web Services - Elastic Beanstalk Application"}], "environment": "Production", "id": "20", "instances": "1", "name": "Amazon Linux 2023 Server", "properties": {"structurizr.dsl.identifier": "2e4949bc-b293-42af-b207-d8c91d04ab64"}, "tags": "Element,Deployment Node,Amazon Web Services - Elastic Beanstalk container"}], "environment": "Production", "id": "19", "instances": "1", "name": "Autoscaling Group", "properties": {"structurizr.dsl.identifier": "c4c7f963-1d8a-4d03-b187-df399fb9fea8"}, "tags": "Element,Deployment Node,Amazon Web Services - Auto Scaling"}], "environment": "Production", "id": "18", "infrastructureNodes": [{"environment": "Production", "id": "22", "name": "Elastic Load Balancer", "properties": {"structurizr.dsl.identifier": "elb"}, "relationships": [{"description": "Forwards requests to", "destinationId": "21", "id": "32", "sourceId": "22", "tags": "Relationship", "technology": "HTTPS"}], "tags": "Element,Infrastructure Node,Amazon Web Services - Elastic Load Balancing"}], "instances": "1", "name": "Amazon Elastic Beanstalk", "properties": {"structurizr.dsl.identifier": "8e6034f1-dc50-42d7-91b8-3ea270a3e764"}, "tags": "Element,Deployment Node,Amazon Web Services - Elastic Beanstalk"}, {"containerInstances": [{"containerId": "5", "deploymentGroups": ["<PERSON><PERSON><PERSON>"], "environment": "Production", "id": "24", "instanceId": 1, "properties": {"structurizr.dsl.identifier": "ea0a37f1-7915-49e8-a619-707429b65cf0"}, "tags": "Container Instance"}], "environment": "Production", "id": "23", "instances": "1", "name": "PostgreSQL RDS", "properties": {"structurizr.dsl.identifier": "ba53ed23-1d1e-4c4f-ba2f-3cdd7a47ed61"}, "tags": "Element,Deployment Node,Amazon Web Services - RDS"}, {"containerInstances": [{"containerId": "6", "deploymentGroups": ["<PERSON><PERSON><PERSON>"], "environment": "Production", "id": "27", "instanceId": 1, "properties": {"structurizr.dsl.identifier": "fb1b665b-5f85-48bd-8c56-c431c14895db"}, "tags": "Container Instance"}], "environment": "Production", "id": "26", "instances": "1", "name": "Rabbit MQ", "properties": {"structurizr.dsl.identifier": "d136f080-3535-4e22-9aea-6877ce54ae6e"}, "tags": "Element,Deployment Node,Amazon Web Services - MQ"}], "environment": "Production", "id": "16", "infrastructureNodes": [{"environment": "Production", "id": "17", "name": "CloudFront", "properties": {"structurizr.dsl.identifier": "frontend_cf"}, "tags": "Element,Infrastructure Node,Amazon Web Services - CloudFront"}], "instances": "1", "name": "Aviatrix Private VPC", "properties": {"structurizr.dsl.identifier": "f521bc67-724d-4de9-b68f-aaab64d03b70"}, "tags": "Element,Deployment Node,Amazon Web Services - VPC Virtual private cloud VPC"}], "environment": "Production", "id": "12", "infrastructureNodes": [{"environment": "Production", "id": "13", "name": "Route 53", "properties": {"structurizr.dsl.identifier": "route53"}, "relationships": [{"description": "Forwards API requests to", "destinationId": "22", "id": "29", "sourceId": "13", "tags": "Relationship", "technology": "HTTPS"}, {"description": "Forwards UI requests to", "destinationId": "17", "id": "30", "sourceId": "13", "tags": "Relationship", "technology": "HTTPS"}], "tags": "Element,Infrastructure Node,Amazon Web Services - Route 53"}], "instances": "1", "name": "EU-West-2", "properties": {"structurizr.dsl.identifier": "e36425ce-6e99-44a1-82f9-40b0f8262ad6"}, "tags": "Element,Deployment Node,Amazon Web Services - Region"}], "environment": "Production", "id": "11", "instances": "1", "name": "Amazon Web Services", "properties": {"structurizr.dsl.identifier": "84144ded-345e-493c-ba0e-9822107020f0"}, "tags": "Element,Deployment Node,Amazon Web Services - Cloud"}], "people": [{"id": "1", "location": "Unspecified", "name": "User", "properties": {"structurizr.dsl.identifier": "u"}, "relationships": [{"description": "Uses", "destinationId": "3", "id": "7", "sourceId": "1", "tags": "Relationship"}, {"description": "Uses", "destinationId": "2", "id": "8", "linkedRelationshipId": "7", "sourceId": "1"}], "tags": "Element,Person"}], "softwareSystems": [{"containers": [{"documentation": {}, "id": "3", "name": "API", "properties": {"structurizr.dsl.identifier": "webapp"}, "relationships": [{"description": "Reads from and writes to", "destinationId": "5", "id": "9", "sourceId": "3", "tags": "Relationship"}, {"description": "Reads from and writes to", "destinationId": "6", "id": "10", "sourceId": "3", "tags": "Relationship"}], "tags": "Element,Container,Amazon Web Services - AWS Elastic Beanstalk Application", "technology": "<PERSON>er - <PERSON>"}, {"documentation": {}, "id": "4", "name": "Frontend UI", "properties": {"structurizr.dsl.identifier": "frontend"}, "tags": "Element,Container", "technology": "Vite React"}, {"documentation": {}, "id": "5", "name": "Database", "properties": {"structurizr.dsl.identifier": "database"}, "tags": "Element,Container", "technology": "Relational database schema"}, {"documentation": {}, "id": "6", "name": "Rabbit MQ", "properties": {"structurizr.dsl.identifier": "rabbitmq"}, "tags": "Element,Container", "technology": "Rabbit MQ"}], "documentation": {}, "id": "2", "location": "Unspecified", "name": "Engage V2", "properties": {"structurizr.dsl.identifier": "s"}, "tags": "Element,Software System"}]}, "name": "Name", "properties": {"structurizr.dsl": "d29ya3NwYWNlIHsKCiAgICBtb2RlbCB7CiAgICAgICAgdSA9IHBlcnNvbiAiVXNlciIKICAgICAgICBzID0gc29mdHdhcmVTeXN0ZW0gIkVuZ2FnZSBWMiIgewogICAgICAgICAgICB3ZWJhcHAgPSBjb250YWluZXIgIkFQSSIgIiIgIkRvY2tlciAtIEdvIiB7CiAgICAgICAgICAgICAgICB0YWdzICJBbWF6b24gV2ViIFNlcnZpY2VzIC0gQVdTIEVsYXN0aWMgQmVhbnN0YWxrIEFwcGxpY2F0aW9uIgogICAgICAgICAgICB9CiAgICAgICAgICAgIGZyb250ZW5kID0gY29udGFpbmVyICJGcm9udGVuZCBVSSIgIiIgIlZpdGUgUmVhY3QiCiAgICAgICAgICAgIGRhdGFiYXNlID0gY29udGFpbmVyICJEYXRhYmFzZSIgIiIgIlJlbGF0aW9uYWwgZGF0YWJhc2Ugc2NoZW1hIgogICAgICAgICAgICByYWJiaXRtcSA9IGNvbnRhaW5lciAiUmFiYml0IE1RIiAiIiAiUmFiYml0IE1RIgogICAgICAgIH0KCiAgICAgICAgdSAtPiB3ZWJhcHAgIlVzZXMiCiAgICAgICAgd2ViYXBwIC0+IGRhdGFiYXNlICJSZWFkcyBmcm9tIGFuZCB3cml0ZXMgdG8iCiAgICAgICAgd2ViYXBwIC0+IHJhYmJpdG1xICJSZWFkcyBmcm9tIGFuZCB3cml0ZXMgdG8iCiAgICAgICAgCiAgICAgICAgcHJvZHVjdGlvbiA9IGRlcGxveW1lbnRFbnZpcm9ubWVudCAiUHJvZHVjdGlvbiIgewogICAgICAgICAgICBkZXBsb3ltZW50Tm9kZSAiQW1hem9uIFdlYiBTZXJ2aWNlcyIgewogICAgICAgICAgICAgICAgdGFncyAiQW1hem9uIFdlYiBTZXJ2aWNlcyAtIENsb3VkIgogICAgICAgICAgICAgICAgCiAgICAgICAgICAgICAgICBkZXBsb3ltZW50Tm9kZSAiRVUtV2VzdC0yIiB7CiAgICAgICAgICAgICAgICAgICAgdGFncyAiQW1hem9uIFdlYiBTZXJ2aWNlcyAtIFJlZ2lvbiIKICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgIHJvdXRlNTMgPSBpbmZyYXN0cnVjdHVyZU5vZGUgIlJvdXRlIDUzIiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRhZ3MgIkFtYXpvbiBXZWIgU2VydmljZXMgLSBSb3V0ZSA1MyIKICAgICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAgIGRlcGxveW1lbnROb2RlICJTMyIgewogICAgICAgICAgICAgICAgICAgICAgICB0YWdzICJBbWF6b24gV2ViIFNlcnZpY2VzIC0gU2ltcGxlIFN0b3JhZ2UgU2VydmljZSBTMyBTdGFuZGFyZCIKCiAgICAgICAgICAgICAgICAgICAgICAgIGZyb250ZW5kX3MzID0gaW5mcmFzdHJ1Y3R1cmVOb2RlICJTMyBCdWNrZXQiIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhZ3MgIkFtYXpvbiBXZWIgU2VydmljZXMgLSBTaW1wbGUgU3RvcmFnZSBTZXJ2aWNlIEJ1Y2tldCIKICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgZGVwbG95bWVudE5vZGUgIkF2aWF0cml4IFByaXZhdGUgVlBDIiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRhZ3MgIkFtYXpvbiBXZWIgU2VydmljZXMgLSBWUEMgVmlydHVhbCBwcml2YXRlIGNsb3VkIFZQQyIKCiAgICAgICAgICAgICAgICAgICAgICAgIGZyb250ZW5kX2NmID0gaW5mcmFzdHJ1Y3R1cmVOb2RlICJDbG91ZEZyb250IiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YWdzICJBbWF6b24gV2ViIFNlcnZpY2VzIC0gQ2xvdWRGcm9udCIKICAgICAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICAgICAgZGVwbG95bWVudE5vZGUgIkFtYXpvbiBFbGFzdGljIEJlYW5zdGFsayIgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFncyAiQW1hem9uIFdlYiBTZXJ2aWNlcyAtIEVsYXN0aWMgQmVhbnN0YWxrIgoKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlcGxveW1lbnROb2RlICJBdXRvc2NhbGluZyBHcm91cCIgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhZ3MgIkFtYXpvbiBXZWIgU2VydmljZXMgLSBBdXRvIFNjYWxpbmciCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXBsb3ltZW50Tm9kZSAiQW1hem9uIExpbnV4IDIwMjMgU2VydmVyIiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhZ3MgIkFtYXpvbiBXZWIgU2VydmljZXMgLSBFbGFzdGljIEJlYW5zdGFsayBjb250YWluZXIiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3ZWJBcHBsaWNhdGlvbkluc3RhbmNlID0gY29udGFpbmVySW5zdGFuY2Ugd2ViYXBwIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhZ3MgIkFtYXpvbiBXZWIgU2VydmljZXMgLSBFbGFzdGljIEJlYW5zdGFsayBBcHBsaWNhdGlvbiIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsYiA9IGluZnJhc3RydWN0dXJlTm9kZSAiRWxhc3RpYyBMb2FkIEJhbGFuY2VyIiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFncyAiQW1hem9uIFdlYiBTZXJ2aWNlcyAtIEVsYXN0aWMgTG9hZCBCYWxhbmNpbmciCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgICAgIGRlcGxveW1lbnROb2RlICJQb3N0Z3JlU1FMIFJEUyIgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFncyAiQW1hem9uIFdlYiBTZXJ2aWNlcyAtIFJEUyIKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRhaW5lckluc3RhbmNlIGRhdGFiYXNlCiAgICAgICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgICAgIGRlcGxveW1lbnROb2RlICJSYWJiaXQgTVEiIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhZ3MgIkFtYXpvbiBXZWIgU2VydmljZXMgLSBNUSIKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRhaW5lckluc3RhbmNlIHJhYmJpdG1xCiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgCiAgICAgICAgICAgIHJvdXRlNTMgLT4gZWxiICJGb3J3YXJkcyBBUEkgcmVxdWVzdHMgdG8iICJIVFRQUyIKICAgICAgICAgICAgcm91dGU1MyAtPiBmcm9udGVuZF9jZiAiRm9yd2FyZHMgVUkgcmVxdWVzdHMgdG8iICJIVFRQUyIKICAgICAgICAgICAgZnJvbnRlbmRfczMgLT4gZnJvbnRlbmRfY2YgIlNlcnZlcyBzdGF0aWMgY29udGVudCBmcm9tIgogICAgICAgICAgICBlbGIgLT4gd2ViQXBwbGljYXRpb25JbnN0YW5jZSAiRm9yd2FyZHMgcmVxdWVzdHMgdG8iICJIVFRQUyIKICAgICAgICB9CiAgICB9CgogICAgdmlld3MgewogICAgICAgIGRlcGxveW1lbnQgcyBwcm9kdWN0aW9uIHsKICAgICAgICAgICAgaW5jbHVkZSAqCiAgICAgICAgICAgIC8vIGF1dG9MYXlvdXQgbHIKICAgICAgICB9CgogICAgICAgIHRoZW1lIGh0dHBzOi8vc3RhdGljLnN0cnVjdHVyaXpyLmNvbS90aGVtZXMvYW1hem9uLXdlYi1zZXJ2aWNlcy0yMDIzLjAxLjMxL3RoZW1lLmpzb24KICAgIH0KICAgIAp9"}, "views": {"configuration": {"branding": {}, "lastSavedView": "Deployment-001", "styles": {}, "terminology": {}, "themes": ["https://static.structurizr.com/themes/amazon-web-services-2023.01.31/theme.json"]}, "deploymentViews": [{"dimensions": {"height": 2023, "width": 3436}, "elements": [{"id": "11", "x": 175, "y": 175}, {"id": "12", "x": 175, "y": 175}, {"id": "13", "x": 305, "y": 655}, {"id": "14", "x": 175, "y": 175}, {"id": "15", "x": 1740, "y": 225}, {"id": "16", "x": 175, "y": 175}, {"id": "17", "x": 1055, "y": 225}, {"id": "18", "x": 175, "y": 175}, {"id": "19", "x": 175, "y": 175}, {"id": "20", "x": 175, "y": 175}, {"id": "21", "x": 1805, "y": 942}, {"id": "22", "x": 1055, "y": 913}, {"id": "23", "x": 175, "y": 175}, {"id": "24", "x": 2705, "y": 1046}, {"id": "26", "x": 175, "y": 175}, {"id": "27", "x": 2705, "y": 433}], "environment": "Production", "generatedKey": true, "key": "Deployment-001", "order": 1, "relationships": [{"id": "25"}, {"id": "28"}, {"id": "29", "vertices": [{"x": 815, "y": 1065}]}, {"id": "30", "vertices": [{"x": 935, "y": 370}]}, {"id": "31"}, {"id": "32"}], "softwareSystemId": "2"}]}}