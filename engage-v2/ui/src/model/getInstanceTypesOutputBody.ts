/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { GetInstanceTypesOutputBodyInstanceTypes } from "./getInstanceTypesOutputBodyInstanceTypes";
import type { GetInstanceTypesOutputBodyMappings } from "./getInstanceTypesOutputBodyMappings";
import type { GetInstanceTypesOutputBodyValidation } from "./getInstanceTypesOutputBodyValidation";

export interface GetInstanceTypesOutputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  instance_types: GetInstanceTypesOutputBodyInstanceTypes;
  mappings: GetInstanceTypesOutputBodyMappings;
  validation: GetInstanceTypesOutputBodyValidation;
}
