package main

import (
	"context"
	"net/http"

	"github.com/danielgtaylor/huma/v2"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	authz "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/handlers/authz"
	userLogsAssignments "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/userLogsAssignment"
)

// Get Node Type URL
type GetUserAssignmentLogsInput struct {
	EngagementID string `path:"engagementID" format:"uuid" doc:"Engagement ID" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
}

// GetUserLogsAssignmentOutput represents the output of the userLogsAssignment route.
type GetUserAssignmentLogsOutput struct {
	Body struct {
		UserAssignmentLogs []userLogsAssignments.UserLogsAssignment `json:"userAssignmentLogs"`
	}
}

func addUserAssignmentLogsRoutes(api huma.API, app *application) {
	huma.Register(api, huma.Operation{
		OperationID:   "get-log-assignments-engagementId",
		Method:        http.MethodGet,
		Path:          "/engagements/{engagementID}/user-assignment-logs",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get User Assignment Log / engagement",
		Tags:          []string{"User Logs Assignment"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *GetUserAssignmentLogsInput) (*GetUserAssignmentLogsOutput, error) {
		// Admin-only access
		if err := authz.RequireAdmin(ctx); err != nil {
			return nil, huma.Error401Unauthorized("Unauthorized")
		}
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		engagementIDIDPgType, err := converters.StringToPgTypeUUID(i.EngagementID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		resp := &GetUserAssignmentLogsOutput{}
		userAssignmentLogs, err := userLogsAssignments.GetAssigmentLogs(app.queries, *engagementIDIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		if err != nil {
			app.logger.Error("Error getting Node URL", "user_id", userID, "error", err.Error())
			return nil, huma.Error404NotFound("Node not found")
		}
		resp.Body.UserAssignmentLogs = userAssignmentLogs
		return resp, nil
	})

	type GetUserAssignmentsLogsOutput struct {
		Body struct {
			UserAssignmentsLogs []userLogsAssignments.UserLogsAssignment `json:"userAssignmentsLogs"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-assignment-logs",
		Method:        http.MethodGet,
		Path:          "/security/user-assignments-logs",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Assignments Logs",
		Tags:          []string{"User Logs Assignment"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetUserAssignmentsLogsOutput, error) {
		// Admin-only access
		if err := authz.RequireAdmin(ctx); err != nil {
			return nil, huma.Error401Unauthorized("Unauthorized")
		}
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp := &GetUserAssignmentsLogsOutput{}
		userAssignmentsLogs, err := userLogsAssignments.GetAllAssignmentsLogs(app.queries)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		if err != nil {
			app.logger.Error("Error getting Node URL", "user_id", userID, "error", err.Error())
			return nil, huma.Error404NotFound("Node not found")
		}

		resp.Body.UserAssignmentsLogs = userAssignmentsLogs
		return resp, nil
	})
}
