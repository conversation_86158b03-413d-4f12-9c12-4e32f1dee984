/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { GetNodeTypeHostOutputBodyActivityLogs } from "./getNodeTypeHostOutputBodyActivityLogs";
import type { NodeHost } from "./nodeHost";

export interface GetNodeTypeHostOutputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  /** Activity Logs */
  activity_logs?: GetNodeTypeHostOutputBodyActivityLogs;
  node: NodeHost;
}
