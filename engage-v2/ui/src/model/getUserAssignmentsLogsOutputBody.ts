/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { GetUserAssignmentsLogsOutputBodyUserAssignmentsLogs } from "./getUserAssignmentsLogsOutputBodyUserAssignmentsLogs";

export interface GetUserAssignmentsLogsOutputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  userAssignmentsLogs: GetUserAssignmentsLogsOutputBodyUserAssignmentsLogs;
}
