# ***************************************************************
// System Entry: Initialization Sequence Complete
// Deploy Engage: Red-Team Engagement Infrastructure Management Application

// Description:
// Written in the highest level of digital code, Engage serves as a central hub for orchestrating and provisioning critical resources,
// ensuring seamless execution of simulated attacks on behalf of our esteemed clients.
# ***************************************************************

# Core Concepts:
# - This should be run against the group's PARENT AWS Organisation account
# - Secrets should be stored in Secrets Manager
# - The UI is deployed to S3
# - The API/backend is deployed as a service to ECS (Fargate "serverless" mode)

locals {
  app_label = "${local.generated_subdomain_safe}-${random_string.random.result}"

  generated_subdomain_safe = lower(replace(var.SUBDOMAIN_NAME, ".", "-"))
  fqdn_api                 = "${var.API_SUBDOMAIN}.${var.SUBDOMAIN_NAME}.${var.DOMAIN_NAME}"
  fqdn_ui                  = "${var.SUBDOMAIN_NAME}.${var.DOMAIN_NAME}"

  s3_origin_id = "${local.app_label}_origin"

  DEFAULT_TAGS = {
    Purpose          = "EngageV2"
    Usage            = "AARO"
    Stage            = "Production"
    ChargeBack       = "Yes"
    ChargeBackPerson = var.person_chargeback
    PoC              = var.person_poc
    DeployedBy       = "SCALR"
  }

  # Variables for the UI
  ui_env = {
    VITE_AZURE_CLIENT_ID = var.UI_AZURE_APP_ID
    VITE_AZURE_AUTHORITY = var.SSO_AZURE_TENANT_ID
    VITE_AZURE_API_SCOPE = "api://${var.SSO_AZURE_API_APP_ID}/${var.UI_AZURE_API_SCOPE}"

    VITE_POST_LOGOUT_REDIRECT_URI = "https://${local.fqdn_ui}"
    VITE_BASE_URL_API             = "https://${local.fqdn_api}"
  }

  # Variables for the API - passed to Docker
  api_env = {
    # General Site Settings
    SECRET_KEY              = "${random_password.secret_key.result}"
    SITE_DOMAIN             = local.fqdn_api
    STANDARD_USERS_GROUP_ID = var.ENGAGE_AZURE_STANDARD_USERS_GRP_ID
    ADMIN_USERS_GROUP_ID    = var.ENGAGE_AZURE_ADMIN_USERS_GRP_ID
    CORS_URL                = "https://${local.fqdn_ui}"
    TERRAFORM_EXEC_PATH     = "/usr/local/bin/terraform"

    # SSO
    AZURE_TENANT_ID     = var.SSO_AZURE_TENANT_ID
    AZURE_APP_ID        = var.SSO_AZURE_API_APP_ID
    AZURE_CLIENT_SECRET = var.SSO_AZURE_API_CLIENT_SECRET

    # DB Settings
    DB_NAME     = random_string.dbName.result
    DB_USER     = random_string.dbUser.result
    DB_PASSWORD = random_password.dbPass.result
    DB_HOST     = aws_db_instance.main_db.address
    DB_PORT     = "5432"

    # AWS credentials for the parent account (to create new accounts)
    AWS_ACCESS_KEY_ID = var.AWS_ACCESS_KEY_ID
    AWS_SECRET_ACCESS_KEY = var.AWS_SECRET_ACCESS_KEY
    AWS_DEFAULT_REGION = var.AWS_REGION

    # Default email alias
    AWS_ROOT_EMAIL = var.AWS_ROOT_EMAIL
    AWS_ROOT_REGION = var.AWS_REGION

    # MQ Settings
    RABBITMQ_USER     = random_string.mq_name.result
    RABBITMQ_PASSWORD = random_password.mq_pass.result
    RABBITMQ_PROTO    = "amqps"
    RABBITMQ_HOST     = split("/", split(":", aws_mq_broker.amq.instances.0.endpoints[0])[1])[2]
    RABBITMQ_PORT     = split(":", aws_mq_broker.amq.instances.0.endpoints[0])[2]
  }

  content_types = {
    css  = "text/css"
    html = "text/html"
    js   = "application/javascript"
    json = "application/json"
    txt  = "text/plain"
  }
}


# ***************************************************************
# * Setup some accessary bits
# ***************************************************************
resource "random_string" "random" {
  length  = 8
  special = false
  upper   = false
  numeric = false
}

resource "random_password" "secret_key" {
  length = 32
}

resource "aws_key_pair" "mgmt_key" {
  key_name   = "${local.app_label}-key"
  public_key = var.ssh_publickey
}


# ***************************************************************
# * Setup Networking variables
# ***************************************************************
data "aws_vpc" "cdi" {
  id = var.NETWORK_VPC_ID
}

data "aws_subnet" "cdi_private1" {
  id = var.NETWORK_SUBNET_1_ID
}

data "aws_subnet" "cdi_private2" {
  id = var.NETWORK_SUBNET_2_ID
}


# ***************************************************************
# * Setup IAM roles for Beanstalk
# ***************************************************************
data "aws_caller_identity" "current" {}

resource "aws_iam_role" "beanstalk_service" {
  name = "${local.app_label}-iam"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "beanstalk_log_attach" {
  role       = aws_iam_role.beanstalk_service.name
  policy_arn = "arn:aws:iam::aws:policy/AWSElasticBeanstalkWebTier"
}

# Policy to allow the EC2 instance to get the Docker creds

data "aws_iam_policy_document" "docker_creds" {
  statement {
    effect    = "Allow"
    actions   = ["s3:GetObject"]
    sid       = ""
    resources = ["${aws_s3_object.gl_docker_auth.arn}"]
  }
  statement {
    effect = "Allow"
    actions = ["kms:Decrypt"]
    sid = ""
    resources = ["${aws_kms_key.bucket_key.arn}"]
  }
}

resource "aws_iam_policy" "docker_creds" {
  name        = "${local.app_label}-bss3pol"
  description = "Policy to allow EBS instance access to S3 docker creds"
  policy      = data.aws_iam_policy_document.docker_creds.json
}

resource "aws_iam_role_policy_attachment" "s3_attach" {
  role       = aws_iam_role.beanstalk_service.name
  policy_arn = aws_iam_policy.docker_creds.arn
}

resource "aws_iam_instance_profile" "beanstalk_iam_instance_profile" {
  name = "${local.app_label}-iamprofile"
  role = aws_iam_role.beanstalk_service.name
}

# ***************************************************************
# * Setup EB S3 bucket
# ***************************************************************
resource "aws_kms_key" "bucket_key" {
  description             = "This key is used to encrypt bucket objects"
  deletion_window_in_days = 10
}

resource "aws_s3_bucket" "deployed_app_ebs" {
  bucket        = local.app_label
  force_destroy = true
  tags = {
    Name = local.app_label
  }
}

resource "aws_s3_bucket_ownership_controls" "deployed_app" {
  bucket = aws_s3_bucket.deployed_app_ebs.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "deployed_app" {
  depends_on = [aws_s3_bucket_ownership_controls.deployed_app]

  bucket = aws_s3_bucket.deployed_app_ebs.id
  acl    = "private"
}

resource "aws_s3_bucket_server_side_encryption_configuration" "deployed_app_enc" {
  bucket = aws_s3_bucket.deployed_app_ebs.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.bucket_key.arn
      sse_algorithm     = "aws:kms"
    }
  }
}

data "template_file" "ecs_config" {
  template = file("deploy/Dockerrun.aws.json")

  vars = {
    image_name = "${var.IMAGE_NAME}:${var.VERSION_TAG}"

    auth_bucket_name = local.app_label
    auth_bukey_key   = "${local.app_label}-dockerauth"
  }
}

# create a zip of your deployment with terraform
data "archive_file" "app_deployment_zip" {
  type        = "zip"
  output_path = "${local.app_label}.zip"

  source {
    content  = data.template_file.ecs_config.rendered
    filename = "Dockerrun.aws.json"
  }
}

resource "aws_s3_object" "app_deployment_zip" {
  bucket = aws_s3_bucket.deployed_app_ebs.id
  source = "${local.app_label}.zip"
  key    = "${local.app_label}-${data.archive_file.app_deployment_zip.output_sha}.zip"
}

data "template_file" "docker_auth" {
  template = file("deploy/dockercfg")

  vars = {
    GITLAB_DOCKER_ACCESS_TOKEN = var.GITLAB_DOCKER_ACCESS_TOKEN
  }
}

resource "aws_s3_object" "gl_docker_auth" {
  bucket  = aws_s3_bucket.deployed_app_ebs.id
  content = data.template_file.docker_auth.rendered
  key     = "${local.app_label}-dockerauth"
}


# ***************************************************************
# * Setup EB
# ***************************************************************
data "aws_elastic_beanstalk_hosted_zone" "current" {
  region = var.AWS_REGION
}

resource "aws_elastic_beanstalk_application" "deployed_app" {
  name        = "${local.app_label}-app"
  description = local.app_label
}

resource "aws_elastic_beanstalk_application_version" "deployed_app_ebs_version" {
  name        = "${local.app_label}-${var.VERSION_TAG}"
  application = aws_elastic_beanstalk_application.deployed_app.name
  bucket      = aws_s3_bucket.deployed_app_ebs.id
  key         = aws_s3_object.app_deployment_zip.id
}

resource "aws_elastic_beanstalk_environment" "deployed_env" {
  name          = "${local.app_label}-env"
  application   = aws_elastic_beanstalk_application.deployed_app.name
  cname_prefix  = local.app_label
  version_label = aws_elastic_beanstalk_application_version.deployed_app_ebs_version.name

  solution_stack_name = var.solution_stack_name

  # Networking
  setting {
    namespace = "aws:ec2:vpc"
    name      = "VPCId"
    value     = data.aws_vpc.cdi.id
    resource  = ""
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "Subnets"
    value     = join(",", sort([data.aws_subnet.cdi_private1.id, data.aws_subnet.cdi_private2.id]))
    resource  = ""
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "ELBSubnets"
    value     = join(",", sort([data.aws_subnet.cdi_private1.id, data.aws_subnet.cdi_private2.id]))
    resource  = ""
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "ELBScheme"
    value     = "internal"
    resource  = ""
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "AssociatePublicIpAddress"
    value     = "false"
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:updatepolicy:rollingupdate"
    name      = "RollingUpdateEnabled"
    value     = "true"
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:updatepolicy:rollingupdate"
    name      = "RollingUpdateType"
    value     = "Health"
    resource  = ""
  }

  setting {
    namespace = "aws:ec2:instances"
    name      = "InstanceTypes"
    value     = var.ec2_instance
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = aws_iam_instance_profile.beanstalk_iam_instance_profile.name
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "SSHSourceRestriction"
    value     = "tcp,22,22,10.0.0.0/8"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment:process:default"
    name      = "HealthCheckPath"
    value     = "/healthcheck"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "EnvironmentType"
    value     = "LoadBalanced"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "LoadBalancerType"
    value     = "application"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:default"
    name      = "ListenerEnabled"
    value     = "false"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "Protocol"
    value     = "HTTPS"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "SSLCertificateArns"
    value     = aws_acm_certificate.cert-api.arn
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "SSLPolicy"
    value     = "ELBSecurityPolicy-TLS13-1-2-2021-06"
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "EC2KeyName"
    value     = aws_key_pair.mgmt_key.key_name
    resource  = ""
  }

  dynamic "setting" {
    for_each = local.api_env
    content {
      namespace = "aws:elasticbeanstalk:application:environment"
      name      = setting.key
      value     = setting.value
      resource  = ""
    }
  }
}


# ***************************************************************
# * Setup AMQ
# ***************************************************************

resource "random_string" "mq_name" {
  length  = 8
  special = false
  upper   = false
  numeric = false
}

resource "random_password" "mq_pass" {
  length  = 16
  special = false
}

# AMQ Security Group (traffic ECS -> AMQ)
resource "aws_security_group" "mq" {
  name        = "${lower(local.app_label)}_mq_sg"
  description = "Allows inbound access from ECS only"
  vpc_id      = data.aws_vpc.cdi.id

  ingress {
    protocol  = "tcp"
    from_port = "443" # RabbitMQ uses 443
    to_port   = "443"
    cidr_blocks = [
      data.aws_subnet.cdi_private1.cidr_block,
      data.aws_subnet.cdi_private2.cidr_block,
    ]
    description = "Permit TCP/443 from ECS hosts"
  }

  ingress {
    protocol  = "tcp"
    from_port = "5671" # RabbitMQ uses 5671
    to_port   = "5671"
    cidr_blocks = [
      data.aws_subnet.cdi_private1.cidr_block,
      data.aws_subnet.cdi_private2.cidr_block,
    ]
    description = "Permit TCP/5671 from ECS hosts"
  }
}

resource "aws_mq_configuration" "mq_cfg" {
  description    = "RabbitMQ config"
  name           = "rabbitmq-broker"
  engine_type    = "RabbitMQ"
  engine_version = "3.13"
  data           = <<DATA
# Default RabbitMQ delivery acknowledgement timeout is 30 minutes in milliseconds
consumer_timeout = 1800000
DATA
}

resource "aws_mq_broker" "amq" {
  broker_name = "${lower(local.app_label)}-amq"

  configuration {
    id       = aws_mq_configuration.mq_cfg.id
    revision = aws_mq_configuration.mq_cfg.latest_revision
  }

  engine_type         = "RabbitMQ"
  engine_version      = "3.13"
  host_instance_type  = var.mq_instance
  security_groups     = [aws_security_group.mq.id]
  subnet_ids          = [data.aws_subnet.cdi_private1.id]
  publicly_accessible = false

  user {
    username = random_string.mq_name.result
    password = random_password.mq_pass.result
  }

  auto_minor_version_upgrade = true
  maintenance_window_start_time {
    day_of_week = "SUNDAY"
    time_of_day = "23:00"
    time_zone   = "UTC"
  }

  apply_immediately = true

  logs {
    general = true
  }
}



# ***************************************************************
# * Setup RDS
# ***************************************************************

resource "random_string" "dbUser" {
  length  = 8
  special = false
  upper   = false
  numeric = false
}

resource "random_string" "dbName" {
  length  = 8
  special = false
  upper   = false
  numeric = false
}

resource "random_password" "dbPass" {
  length  = 16
  special = false
}

# RDS Security Group (traffic ECS -> RDS)
resource "aws_security_group" "rds" {
  name        = "${lower(local.app_label)}_rds_sg"
  description = "Allows inbound access from ECS only"
  vpc_id      = data.aws_vpc.cdi.id

  ingress {
    protocol  = "tcp"
    from_port = "5432"
    to_port   = "5432"
    cidr_blocks = [
      data.aws_subnet.cdi_private1.cidr_block,
      data.aws_subnet.cdi_private2.cidr_block,
    ]
    description = "Permit TCP/5432 from ECS hosts"
  }
}

resource "aws_db_subnet_group" "db" {
  name       = lower(local.app_label)
  subnet_ids = [data.aws_subnet.cdi_private1.id, data.aws_subnet.cdi_private2.id, ]
}

resource "aws_db_instance" "main_db" {
  identifier                 = "${lower(local.app_label)}-db"
  db_name                    = random_string.dbName.result
  username                   = random_string.dbUser.result
  password                   = random_password.dbPass.result
  engine                     = "postgres"
  engine_version             = "17.4"
  instance_class             = var.db_instance
  allocated_storage          = var.db_size
  vpc_security_group_ids     = [aws_security_group.rds.id]
  db_subnet_group_name       = aws_db_subnet_group.db.name
  multi_az                   = false
  storage_type               = "gp2"
  publicly_accessible        = false
  storage_encrypted          = true
  backup_retention_period    = 7
  skip_final_snapshot        = true
  auto_minor_version_upgrade = true
  parameter_group_name       = aws_db_parameter_group.rds-setting.name
}


resource "aws_db_parameter_group" "rds-setting" {
  name   = "${lower(local.app_label)}-rdsset"
  family = "postgres17"

  parameter {
    name  = "log_connections"
    value = "1"
  }
}

# ***************************************************************
# * Setup DNS
# ***************************************************************

data "aws_route53_zone" "parent" {
  name = var.DOMAIN_NAME
}

resource "aws_route53_record" "api" {
  zone_id = data.aws_route53_zone.parent.id
  name    = local.fqdn_api
  type    = "A"

  alias {
    name                   = aws_elastic_beanstalk_environment.deployed_env.cname
    zone_id                = data.aws_elastic_beanstalk_hosted_zone.current.id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "ui" {
  zone_id = data.aws_route53_zone.parent.id
  name    = "${var.SUBDOMAIN_NAME}.${var.DOMAIN_NAME}"
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.s3_distribution.domain_name
    zone_id                = aws_cloudfront_distribution.s3_distribution.hosted_zone_id
    evaluate_target_health = false
  }
}

# ***************************************************************
# * Setup TLS - UI
# ***************************************************************
resource "aws_acm_certificate" "cert" {
  domain_name       = "${var.SUBDOMAIN_NAME}.${var.DOMAIN_NAME}"
  validation_method = "DNS"
  provider          = aws.virginia

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_route53_record" "certValidation" {
  for_each = {
    for dvo in aws_acm_certificate.cert.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = data.aws_route53_zone.parent.id
}

# ***************************************************************
# * Setup TLS - API
# ***************************************************************
resource "aws_acm_certificate" "cert-api" {
  domain_name       = local.fqdn_api
  validation_method = "DNS"

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_route53_record" "certValidation-api" {
  for_each = {
    for dvo in aws_acm_certificate.cert-api.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = data.aws_route53_zone.parent.id
}


# ***************************************************************
# * Pull UI release from GL
# ***************************************************************

# This gets the release package link
data "gitlab_release_links" "engage-v2" {
  project  = var.GITLAB_PROJECT_ID
  tag_name = var.VERSION_TAG
}

# This downloads it
data "http" "ui_release" {
  for_each = {
    for link in data.gitlab_release_links.engage-v2.release_links : "${link["name"]}" => link if link.link_type == "other"
  }

  url = each.value.url
  request_headers = {
    Accept        = "application/json"
    PRIVATE-TOKEN = var.GITLAB_ACCESS_TOKEN
  }

  lifecycle {
    postcondition {
      condition     = contains([200, 201, 204], self.status_code)
      error_message = "UI Release Link returned invalid status code: ${each.value.url}"
    }
  }
}

# This one uploads the non .js files 
resource "aws_s3_object" "ui_release_upload" {
  for_each = {
    for filename, downloaded_file in data.http.ui_release : filename => downloaded_file if endswith(filename, ".js") == false
  }
  bucket         = aws_s3_bucket.ui.id
  key            = each.key
  content_type   = lookup(local.content_types, element(split(".", each.key), length(split(".", each.key)) - 1), "text/plain")
  content_base64 = each.value.response_body_base64
}

# This one uploads the JS file(s) and replaces values.
resource "aws_s3_object" "ui_release_upload_js" {
  for_each = {
    for filename, downloaded_file in data.http.ui_release : filename => downloaded_file if endswith(filename, ".js")
  }
  bucket       = aws_s3_bucket.ui.id
  key          = each.key
  content_type = lookup(local.content_types, element(split(".", each.key), length(split(".", each.key)) - 1), "text/plain")
  # This is ugly but it's needed to replace the hard-coded values
  content = replace(replace(replace(replace(replace(replace(each.value.response_body,
    # content = endswith(each.key, ".js") ? replace(replace(replace(replace(replace("ggrr",
    "xxx-xxx-xxx-xxx-xxx", local.ui_env.VITE_AZURE_CLIENT_ID),
    "yyy-yyy-yyy-yyy-yyy", local.ui_env.VITE_AZURE_AUTHORITY),
    "api://zzz-zzz-zzz-zzz/user_impersonation", local.ui_env.VITE_AZURE_API_SCOPE),
    "https://myfirst.logout.domain", "https://${local.fqdn_ui}"),
    "https://myfirst.base.url.domain", "https://${local.fqdn_api}"),
  "http://localhost:8080", "https://${local.fqdn_api}")

}

# ***************************************************************
# * Setup S3 Bucket for UI files
# ***************************************************************
resource "aws_s3_bucket" "ui" {
  bucket = "${local.app_label}-ui"
  tags = {
    Name = "${local.app_label}-ui"
  }
  force_destroy = true
}

resource "aws_s3_bucket_cors_configuration" "example" {
  bucket = aws_s3_bucket.ui.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "HEAD"]
    allowed_origins = ["https://${var.SUBDOMAIN_NAME}.${var.DOMAIN_NAME}"]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

resource "aws_iam_access_key" "ui_iam" {
  user = aws_iam_user.ui_iam.name
}

resource "aws_iam_user" "ui_iam" {
  name = local.app_label

  tags = {
    Type = "AppID"
  }
}

resource "aws_iam_user_policy" "ui_iam_perms" {
  name = "${local.app_label}_perms"
  user = aws_iam_user.ui_iam.name

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "VisualEditor0",
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:GetObjectAcl",
                "s3:GetObject",
                "s3:ListBucket",
                "s3:DeleteObject",
                "s3:PutObjectAcl"
            ],
            "Resource": [
                  "${aws_s3_bucket.ui.arn}",
                  "${aws_s3_bucket.ui.arn}/*"
            ]
        }
    ]
}
EOF
}

# ***************************************************************
# * Setup CF for serving files from the ui bucket
# ***************************************************************

resource "tls_private_key" "signing_key" {
  algorithm = "RSA"
  rsa_bits  = 2048
}

resource "aws_cloudfront_origin_access_identity" "cf_ui" {
  comment = "${local.app_label} ui Files"
}

resource "aws_cloudfront_response_headers_policy" "cf_ui" {
  name = "${lower(local.app_label)}_cf_cors"

  cors_config {
    access_control_allow_credentials = true

    access_control_allow_headers {
      items = ["origin", "content-type", "accept"]
    }

    access_control_allow_methods {
      items = ["GET", "HEAD", "OPTIONS"]
    }

    access_control_allow_origins {
      items = ["${var.SUBDOMAIN_NAME}.${var.DOMAIN_NAME}"]
    }

    origin_override = true
  }
}

resource "aws_s3_bucket_policy" "cf_allow_access" {
  bucket = aws_s3_bucket.ui.id
  policy = data.aws_iam_policy_document.cf_allow_access.json
}

data "aws_iam_policy_document" "cf_allow_access" {
  statement {
    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.cf_ui.iam_arn]
    }

    actions = [
      "s3:GetObject",
      "s3:ListBucket",
    ]

    resources = [
      "${aws_s3_bucket.ui.arn}",
      "${aws_s3_bucket.ui.arn}/*",
    ]
  }

  statement {
    principals {
      type        = "AWS"
      identifiers = [aws_iam_user.ui_iam.arn]
    }

    actions = [
      "s3:PutObject",
      "s3:GetObjectAcl",
      "s3:GetObject",
      "s3:ListBucket",
      "s3:DeleteObject",
      "s3:PutObjectAcl"
    ]

    resources = [
      "${aws_s3_bucket.ui.arn}",
      "${aws_s3_bucket.ui.arn}/*",
    ]
  }
}

resource "aws_cloudfront_distribution" "s3_distribution" {
  origin {
    domain_name = aws_s3_bucket.ui.bucket_regional_domain_name
    origin_id   = local.s3_origin_id

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.cf_ui.cloudfront_access_identity_path
    }
  }

  enabled             = true
  comment             = "${local.app_label} ui CF"
  default_root_object = "index.html"
  aliases             = ["${var.SUBDOMAIN_NAME}.${var.DOMAIN_NAME}"]

  default_cache_behavior {
    # Use CachedDisabeld managed policy
    cache_policy_id  = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad"
    target_origin_id = local.s3_origin_id

    allowed_methods = ["GET", "HEAD"]
    cached_methods  = ["GET", "HEAD"]

    viewer_protocol_policy = "redirect-to-https"
    compress               = true
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
  }

  custom_error_response {
    error_code            = 403
    response_page_path    = "/index.html"
    response_code         = 200
    error_caching_min_ttl = 300
  }

  custom_error_response {
    error_code            = 404
    response_page_path    = "/index.html"
    response_code         = 200
    error_caching_min_ttl = 300
  }

  price_class = "PriceClass_200"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  tags = {
    Environment = "production"
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = aws_acm_certificate.cert.arn
    ssl_support_method             = "sni-only"
    minimum_protocol_version       = "TLSv1.2_2021"
  }
}

# ***************************************************************
# * Data sources to populate outputs
# ***************************************************************

data "aws_instance" "eb_instances" {
  depends_on  = [aws_elastic_beanstalk_environment.deployed_env]
  instance_id = aws_elastic_beanstalk_environment.deployed_env.instances[0]
}
