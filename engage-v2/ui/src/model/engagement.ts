/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { EngagementNodeGroups } from "./engagementNodeGroups";
import type { EngagementUsers } from "./engagementUsers";

export interface Engagement {
  client_name: string;
  created_at: string;
  error_message: string;
  id: string;
  is_active: boolean;
  node_groups?: EngagementNodeGroups;
  status: string;
  title: string;
  updated_at: string;
  users: EngagementUsers;
  wbs_code: string;
}
