/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { GetNodeTypePersonOutputBodyActivityLogs } from "./getNodeTypePersonOutputBodyActivityLogs";
import type { <PERSON>de<PERSON><PERSON> } from "./nodePerson";

export interface GetNodeTypePersonOutputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  /** Activity Logs */
  activity_logs?: GetNodeTypePersonOutputBodyActivityLogs;
  node: Node<PERSON>erson;
}
