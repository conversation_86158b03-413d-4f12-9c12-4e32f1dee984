/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { CreateEngagementInputBodyUsernames } from "./createEngagementInputBodyUsernames";

export interface CreateEngagementInputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  client_name: string;
  /** @maxLength 250 */
  engagement_title: string;
  usernames: CreateEngagementInputBodyUsernames;
  wbs_code: string;
}
