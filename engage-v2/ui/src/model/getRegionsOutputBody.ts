/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { GetRegionsOutputBodyPrioritizedRegions } from "./getRegionsOutputBodyPrioritizedRegions";
import type { GetRegionsOutputBodyRegions } from "./getRegionsOutputBodyRegions";

export interface GetRegionsOutputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  prioritizedRegions: GetRegionsOutputBodyPrioritizedRegions;
  regions: GetRegionsOutputBodyRegions;
}
