export const createRefetchStatus =
  <T extends Record<string, any>>(
    collectionKey: string,
    options: { refetchIntervalSeconds?: number; statusField?: string } = {},
  ) =>
  (data: any): number | false => {
    const _interval = options.refetchIntervalSeconds ?? 30000;
    const statusField = options.statusField;

    // Try to extract the collection
    let collection = data?.state?.data?.[collectionKey];
    if (!collection && Array.isArray(data?.state?.data)) {
      collection = data.state.data;
    }
    if (!collection && Array.isArray(data)) {
      collection = data;
    }

    if (!collection || !Array.isArray(collection)) {
      return _interval;
    }

    const allowedStatuses = ["SUCCESS", "ACTIVE", "ERROR", "WARNING"];
    const shouldRefetch = collection.some((item: T) => {
      const status = statusField ? item[statusField] : undefined;
      return !allowedStatuses.includes(status);
    });

    return shouldRefetch ? _interval : false;
  };

// Specialized helper for cloud instance state polling on engagement page
export const createRefetchCloudInstances = (options: { refetchIntervalMs?: number; transientStates?: string[] } = {}) => {
  const interval = options.refetchIntervalMs ?? 3000;
  const transient = new Set((options.transientStates ?? ["pending", "stopping"]).map((s) => s.toLowerCase()));
  return (data: any): number | false => {
    // Try common shapes
    const groups =
      data?.cloud_instance_node_groups ||
      data?.state?.data?.cloud_instance_node_groups ||
      [];

    if (!Array.isArray(groups)) return interval;

    for (const ng of groups) {
      const cis = ng?.cloud_instances || [];
      for (const ci of cis) {
        const s = (ci?.cloud_instance_state || "").toLowerCase();
        if (transient.has(s)) return interval;
      }
    }
    return false;
  };
};
