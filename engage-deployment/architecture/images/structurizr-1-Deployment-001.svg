<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 3436 2023" style="background: #ffffff"><defs><style>@import url(https://fonts.googleapis.com/css?family=Open+Sans:400,700);</style></defs><defs joint-selector="defs"></defs><g joint-selector="layers"  transform="matrix(1,0,0,1,0,0)"><g ></g><g ><!--z-index:-8--><!--z-index:-7--><!--z-index:-6--><!--z-index:-5--><!--z-index:-4--><!--z-index:-3--><!--z-index:-2--><!--z-index:-1--><!--z-index:0--><!--z-index:1--><!--z-index:2--><!--z-index:3--><!--z-index:4--><!--z-index:5--><!--z-index:6--><!--z-index:7--><!--z-index:8--><!--z-index:9--><!--z-index:10--><!--z-index:11--><!--z-index:12--><!--z-index:13--><!--z-index:14--><!--z-index:15--><g   id="j_10"  transform="translate(265,165)" style=""><g  id="v-62" style="opacity: 1;"><rect  id="v-63" width="2970" height="1623" rx="10" ry="10" fill="#ffffff" stroke="#888888" stroke-width="1" pointer-events="none" style="stroke: rgb(136, 136, 136);"></rect><text  id="v-64" font-size="24px" xml:space="preserve" y="1584" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,15,0)" style="fill: rgb(0, 0, 0);"><tspan dy="0" >Amazon Web Services</tspan></text><text  id="v-65" font-size="17px" xml:space="preserve" y="1608" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,15,0)" style="fill: rgb(0, 0, 0);"><tspan dy="0" >[Deployment Node]</tspan></text><text  id="v-66" font-size="40px" xml:space="preserve" display="none" y="1608" font-weight="normal" fill="#000000" text-anchor="end" pointer-events="visible" font-family="Open Sans" transform="matrix(1,0,0,1,2955,0)"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><image  id="v-67" pointer-events="visible"></image></g></g><!--z-index:16--><g   id="j_11"  transform="translate(285,185)" style=""><g  id="v-68" style="opacity: 1;"><rect  id="v-69" width="2930" height="1512" rx="10" ry="10" fill="#ffffff" stroke="#888888" stroke-width="1" pointer-events="none" style="stroke: rgb(136, 136, 136);"></rect><text  id="v-70" font-size="24px" xml:space="preserve" y="1473" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,15,0)" style="fill: rgb(0, 0, 0);"><tspan dy="0" >EU-West-2</tspan></text><text  id="v-71" font-size="17px" xml:space="preserve" y="1497" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,15,0)" style="fill: rgb(0, 0, 0);"><tspan dy="0" >[Deployment Node]</tspan></text><text  id="v-72" font-size="40px" xml:space="preserve" display="none" y="1497" font-weight="normal" fill="#000000" text-anchor="end" pointer-events="visible" font-family="Open Sans" transform="matrix(1,0,0,1,2915,0)"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><image  id="v-73" pointer-events="visible"></image></g></g><!--z-index:17--><g   id="j_3"  transform="translate(305,655)" style=""><g  id="v-7" style="opacity: 1;"><rect  id="v-8" rx="1" ry="1" stroke="#7545d1" stroke-width="2" pointer-events="visiblePainted" fill="#dddddd" width="450" height="300" style="fill: rgb(221, 221, 221); stroke: rgb(117, 69, 209);"></rect><text  id="v-9" font-size="34" xml:space="preserve" y="0.8em" font-weight="bold" text-anchor="middle" pointer-events="visible" font-family="Open Sans" fill="#7545d1" transform="matrix(1,0,0,1,225,84.4)" style="fill: rgb(117, 69, 209);"><tspan dy="0" >Route 53</tspan></text><text  id="v-10" font-size="17" xml:space="preserve" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#7545d1" transform="matrix(1,0,0,1,225,125.2)" style="fill: rgb(117, 69, 209);"><tspan dy="0" >[Infrastructure Node]</tspan></text><text  id="v-11" font-size="24" xml:space="preserve" display="none" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#7545d1" transform="matrix(1,0,0,1,225,145.6)" style="fill: rgb(117, 69, 209);"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><g  id="v-12" color="#7545d1" transform="matrix(1,0,0,1,222.5,268)" display="none" style="fill: rgb(117, 69, 209);"><g ></g><g ></g><g ></g><g ></g></g><image  id="v-13" xlink:href="https://static.structurizr.com/themes/amazon-web-services-2023.01.31/Arch_Amazon-Route-53_48.png" width="60" height="60" opacity="1" transform="matrix(1,0,0,1,195,155.6)"></image></g></g><!--z-index:18--><g   id="j_12"  transform="translate(1720,205)" style=""><g  id="v-74" style="opacity: 1;"><rect  id="v-75" width="490" height="411" rx="10" ry="10" fill="#ffffff" stroke="#3f8624" stroke-width="1" pointer-events="none" style="stroke: rgb(63, 134, 36);"></rect><text  id="v-76" font-size="24px" xml:space="preserve" y="372" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#3f8624" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(63, 134, 36);"><tspan dy="0" >S3</tspan></text><text  id="v-77" font-size="17px" xml:space="preserve" y="396" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#3f8624" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(63, 134, 36);"><tspan dy="0" >[Deployment Node]</tspan></text><text  id="v-78" font-size="40px" xml:space="preserve" display="none" y="396" font-weight="normal" fill="#3f8624" text-anchor="end" pointer-events="visible" font-family="Open Sans" transform="matrix(1,0,0,1,475,0)"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><image  id="v-79" pointer-events="visible" xlink:href="https://static.structurizr.com/themes/amazon-web-services-2023.01.31/Res_Amazon-Simple-Storage-Service_S3-Standard_48_Light.png" width="48" height="48" opacity="1" x="15" y="353"></image></g></g><!--z-index:19--><g   id="j_13"  transform="translate(1015,205)" style=""><g  id="v-80" style="opacity: 1;"><rect  id="v-81" width="2180" height="1401" rx="10" ry="10" fill="#ffffff" stroke="#4d27aa" stroke-width="1" pointer-events="none" style="stroke: rgb(77, 39, 170);"></rect><text  id="v-82" font-size="24px" xml:space="preserve" y="1362" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#4d27aa" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(77, 39, 170);"><tspan dy="0" >Aviatrix Private VPC</tspan></text><text  id="v-83" font-size="17px" xml:space="preserve" y="1386" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#4d27aa" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(77, 39, 170);"><tspan dy="0" >[Deployment Node]</tspan></text><text  id="v-84" font-size="40px" xml:space="preserve" display="none" y="1386" font-weight="normal" fill="#4d27aa" text-anchor="end" pointer-events="visible" font-family="Open Sans" transform="matrix(1,0,0,1,2165,0)"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><image  id="v-85" pointer-events="visible" xlink:href="https://static.structurizr.com/themes/amazon-web-services-2023.01.31/Res_Amazon-VPC_Virtual-private-cloud-VPC_48_Light.png" width="48" height="48" opacity="1" x="15" y="1343"></image></g></g><!--z-index:20--><g   id="j_4"  transform="translate(1740,225)" style=""><g  id="v-14" style="opacity: 1;"><rect  id="v-15" rx="1" ry="1" stroke="#3f8624" stroke-width="2" pointer-events="visiblePainted" fill="#dddddd" width="450" height="300" style="fill: rgb(221, 221, 221); stroke: rgb(63, 134, 36);"></rect><text  id="v-16" font-size="34" xml:space="preserve" y="0.8em" font-weight="bold" text-anchor="middle" pointer-events="visible" font-family="Open Sans" fill="#3f8624" transform="matrix(1,0,0,1,225,84.4)" style="fill: rgb(63, 134, 36);"><tspan dy="0" >S3 Bucket</tspan></text><text  id="v-17" font-size="17" xml:space="preserve" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#3f8624" transform="matrix(1,0,0,1,225,125.2)" style="fill: rgb(63, 134, 36);"><tspan dy="0" >[Infrastructure Node]</tspan></text><text  id="v-18" font-size="24" xml:space="preserve" display="none" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#3f8624" transform="matrix(1,0,0,1,225,145.6)" style="fill: rgb(63, 134, 36);"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><g  id="v-19" color="#3f8624" transform="matrix(1,0,0,1,222.5,268)" display="none" style="fill: rgb(63, 134, 36);"><g ></g><g ></g><g ></g><g ></g></g><image  id="v-20" xlink:href="https://static.structurizr.com/themes/amazon-web-services-2023.01.31/Res_Amazon-Simple-Storage-Service_Bucket_48_Light.png" width="60" height="60" opacity="1" transform="matrix(1,0,0,1,195,155.6)"></image></g></g><!--z-index:21--><g   id="j_5"  transform="translate(1055,225)" style=""><g  id="v-21" style="opacity: 1;"><rect  id="v-22" rx="1" ry="1" stroke="#7545d1" stroke-width="2" pointer-events="visiblePainted" fill="#dddddd" width="450" height="300" style="fill: rgb(221, 221, 221); stroke: rgb(117, 69, 209);"></rect><text  id="v-23" font-size="34" xml:space="preserve" y="0.8em" font-weight="bold" text-anchor="middle" pointer-events="visible" font-family="Open Sans" fill="#7545d1" transform="matrix(1,0,0,1,225,84.4)" style="fill: rgb(117, 69, 209);"><tspan dy="0" >CloudFront</tspan></text><text  id="v-24" font-size="17" xml:space="preserve" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#7545d1" transform="matrix(1,0,0,1,225,125.2)" style="fill: rgb(117, 69, 209);"><tspan dy="0" >[Infrastructure Node]</tspan></text><text  id="v-25" font-size="24" xml:space="preserve" display="none" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#7545d1" transform="matrix(1,0,0,1,225,145.6)" style="fill: rgb(117, 69, 209);"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><g  id="v-26" color="#7545d1" transform="matrix(1,0,0,1,222.5,268)" display="none" style="fill: rgb(117, 69, 209);"><g ></g><g ></g><g ></g><g ></g></g><image  id="v-27" xlink:href="https://static.structurizr.com/themes/amazon-web-services-2023.01.31/Arch_Amazon-CloudFront_48.png" width="60" height="60" opacity="1" transform="matrix(1,0,0,1,195,155.6)"></image></g></g><!--z-index:22--><g   id="j_14"  transform="translate(1035,882)" style=""><g  id="v-86" style="opacity: 1;"><rect  id="v-87" width="1280" height="633" rx="10" ry="10" fill="#ffffff" stroke="#e5770d" stroke-width="1" pointer-events="none" style="stroke: rgb(229, 119, 13);"></rect><text  id="v-88" font-size="24px" xml:space="preserve" y="594" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#e5770d" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(229, 119, 13);"><tspan dy="0" >Amazon Elastic Beanstalk</tspan></text><text  id="v-89" font-size="17px" xml:space="preserve" y="618" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#e5770d" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(229, 119, 13);"><tspan dy="0" >[Deployment Node]</tspan></text><text  id="v-90" font-size="40px" xml:space="preserve" display="none" y="618" font-weight="normal" fill="#e5770d" text-anchor="end" pointer-events="visible" font-family="Open Sans" transform="matrix(1,0,0,1,1265,0)"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><image  id="v-91" pointer-events="visible" xlink:href="https://static.structurizr.com/themes/amazon-web-services-2023.01.31/Arch_AWS-Elastic-Beanstalk_48.png" width="48" height="48" opacity="1" x="15" y="575"></image></g></g><!--z-index:23--><g   id="j_17"  transform="translate(2685,1026)" style=""><g  id="v-104" style="opacity: 1;"><rect  id="v-105" width="490" height="411" rx="10" ry="10" fill="#ffffff" stroke="#3f51d4" stroke-width="1" pointer-events="none" style="stroke: rgb(63, 81, 212);"></rect><text  id="v-106" font-size="24px" xml:space="preserve" y="372" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#3f51d4" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(63, 81, 212);"><tspan dy="0" >PostgreSQL RDS</tspan></text><text  id="v-107" font-size="17px" xml:space="preserve" y="396" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#3f51d4" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(63, 81, 212);"><tspan dy="0" >[Deployment Node]</tspan></text><text  id="v-108" font-size="40px" xml:space="preserve" display="none" y="396" font-weight="normal" fill="#3f51d4" text-anchor="end" pointer-events="visible" font-family="Open Sans" transform="matrix(1,0,0,1,475,0)"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><image  id="v-109" pointer-events="visible" xlink:href="https://static.structurizr.com/themes/amazon-web-services-2023.01.31/Arch_Amazon-RDS_48.png" width="48" height="48" opacity="1" x="15" y="353"></image></g></g><!--z-index:24--><g   id="j_18"  transform="translate(2685,413)" style=""><g  id="v-110" style="opacity: 1;"><rect  id="v-111" width="490" height="411" rx="10" ry="10" fill="#ffffff" stroke="#da2e6e" stroke-width="1" pointer-events="none" style="stroke: rgb(218, 46, 110);"></rect><text  id="v-112" font-size="24px" xml:space="preserve" y="372" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#da2e6e" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(218, 46, 110);"><tspan dy="0" >Rabbit MQ</tspan></text><text  id="v-113" font-size="17px" xml:space="preserve" y="396" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#da2e6e" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(218, 46, 110);"><tspan dy="0" >[Deployment Node]</tspan></text><text  id="v-114" font-size="40px" xml:space="preserve" display="none" y="396" font-weight="normal" fill="#da2e6e" text-anchor="end" pointer-events="visible" font-family="Open Sans" transform="matrix(1,0,0,1,475,0)"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><image  id="v-115" pointer-events="visible" xlink:href="https://static.structurizr.com/themes/amazon-web-services-2023.01.31/Arch_Amazon-MQ_48.png" width="48" height="48" opacity="1" x="15" y="353"></image></g></g><!--z-index:25--><g   id="j_7"  transform="translate(1055,913)" style=""><g  id="v-38" style="opacity: 1;"><rect  id="v-39" rx="1" ry="1" stroke="#7545d1" stroke-width="2" pointer-events="visiblePainted" fill="#dddddd" width="450" height="300" style="fill: rgb(221, 221, 221); stroke: rgb(117, 69, 209);"></rect><text  id="v-40" font-size="34" xml:space="preserve" y="0.8em" font-weight="bold" text-anchor="middle" pointer-events="visible" font-family="Open Sans" fill="#7545d1" transform="matrix(1,0,0,1,225,84.4)" style="fill: rgb(117, 69, 209);"><tspan dy="0" >Elastic Load Balancer</tspan></text><text  id="v-41" font-size="17" xml:space="preserve" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#7545d1" transform="matrix(1,0,0,1,225,125.2)" style="fill: rgb(117, 69, 209);"><tspan dy="0" >[Infrastructure Node]</tspan></text><text  id="v-42" font-size="24" xml:space="preserve" display="none" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#7545d1" transform="matrix(1,0,0,1,225,145.6)" style="fill: rgb(117, 69, 209);"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><g  id="v-43" color="#7545d1" transform="matrix(1,0,0,1,222.5,268)" display="none" style="fill: rgb(117, 69, 209);"><g ></g><g ></g><g ></g><g ></g></g><image  id="v-44" xlink:href="https://static.structurizr.com/themes/amazon-web-services-2023.01.31/Arch_Elastic-Load-Balancing_48.png" width="60" height="60" opacity="1" transform="matrix(1,0,0,1,195,155.6)"></image></g></g><!--z-index:26--><g   id="j_15"  transform="translate(1765,902)" style=""><g  id="v-92" style="opacity: 1;"><rect  id="v-93" width="530" height="522" rx="10" ry="10" fill="#ffffff" stroke="#da2e6e" stroke-width="1" pointer-events="none" style="stroke: rgb(218, 46, 110);"></rect><text  id="v-94" font-size="24px" xml:space="preserve" y="483" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#da2e6e" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(218, 46, 110);"><tspan dy="0" >Autoscaling Group</tspan></text><text  id="v-95" font-size="17px" xml:space="preserve" y="507" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#da2e6e" transform="matrix(1,0,0,1,73,0)" style="fill: rgb(218, 46, 110);"><tspan dy="0" >[Deployment Node]</tspan></text><text  id="v-96" font-size="40px" xml:space="preserve" display="none" y="507" font-weight="normal" fill="#da2e6e" text-anchor="end" pointer-events="visible" font-family="Open Sans" transform="matrix(1,0,0,1,515,0)"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><image  id="v-97" pointer-events="visible" xlink:href="https://static.structurizr.com/themes/amazon-web-services-2023.01.31/Arch_AWS-Auto-Scaling_48.png" width="48" height="48" opacity="1" x="15" y="464"></image></g></g><!--z-index:27--><g   id="j_8"  transform="translate(2705,1046)" style=""><g  id="v-48" style="opacity: 1;"><rect  id="v-49" rx="1" ry="1" stroke="#c7c7c7" stroke-width="2" pointer-events="visiblePainted" fill="#dddddd" width="450" height="300" style="fill: rgb(221, 221, 221); stroke: rgb(199, 199, 199);"></rect><text  id="v-50" font-size="34" xml:space="preserve" y="0.8em" font-weight="bold" text-anchor="middle" pointer-events="visible" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,225,121.9)" style="fill: rgb(0, 0, 0);"><tspan dy="0" >Database</tspan></text><text  id="v-51" font-size="17" xml:space="preserve" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,225,162.7)" style="fill: rgb(0, 0, 0);"><tspan dy="0" >[Container: Relational database schema]</tspan></text><text  id="v-52" font-size="24" xml:space="preserve" display="none" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,225,183.1)" style="fill: rgb(0, 0, 0);"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><g  id="v-53" color="#000000" transform="matrix(1,0,0,1,222.5,268)" display="none" style="fill: rgb(0, 0, 0);"><g ></g><g ></g><g ></g><g ></g></g><image  id="v-54"></image></g></g><!--z-index:28--><g   id="j_9"  transform="translate(2705,433)" style=""><g  id="v-55" style="opacity: 1;"><rect  id="v-56" rx="1" ry="1" stroke="#c7c7c7" stroke-width="2" pointer-events="visiblePainted" fill="#dddddd" width="450" height="300" style="fill: rgb(221, 221, 221); stroke: rgb(199, 199, 199);"></rect><text  id="v-57" font-size="34" xml:space="preserve" y="0.8em" font-weight="bold" text-anchor="middle" pointer-events="visible" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,225,121.9)" style="fill: rgb(0, 0, 0);"><tspan dy="0" >Rabbit MQ</tspan></text><text  id="v-58" font-size="17" xml:space="preserve" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,225,162.7)" style="fill: rgb(0, 0, 0);"><tspan dy="0" >[Container: Rabbit MQ]</tspan></text><text  id="v-59" font-size="24" xml:space="preserve" display="none" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,225,183.1)" style="fill: rgb(0, 0, 0);"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><g  id="v-60" color="#000000" transform="matrix(1,0,0,1,222.5,268)" display="none" style="fill: rgb(0, 0, 0);"><g ></g><g ></g><g ></g><g ></g></g><image  id="v-61"></image></g></g><!--z-index:29--><g   id="j_16"  transform="translate(1785,922)" style=""><g  id="v-98" style="opacity: 1;"><rect  id="v-99" width="490" height="411" rx="10" ry="10" fill="#ffffff" stroke="#888888" stroke-width="1" pointer-events="none" style="stroke: rgb(136, 136, 136);"></rect><text  id="v-100" font-size="24px" xml:space="preserve" y="372" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,15,0)" style="fill: rgb(0, 0, 0);"><tspan dy="0" >Amazon Linux 2023 Server</tspan></text><text  id="v-101" font-size="17px" xml:space="preserve" y="396" font-weight="normal" text-anchor="start" pointer-events="visible" font-family="Open Sans" fill="#000000" transform="matrix(1,0,0,1,15,0)" style="fill: rgb(0, 0, 0);"><tspan dy="0" >[Deployment Node]</tspan></text><text  id="v-102" font-size="40px" xml:space="preserve" display="none" y="396" font-weight="normal" fill="#000000" text-anchor="end" pointer-events="visible" font-family="Open Sans" transform="matrix(1,0,0,1,475,0)"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><image  id="v-103" pointer-events="visible"></image></g></g><!--z-index:30--><g   id="j_6"  transform="translate(1805,942)" style=""><g  id="v-28" style="opacity: 1;"><rect  id="v-29" rx="1" ry="1" stroke="#d45b07" stroke-width="2" pointer-events="visiblePainted" fill="#dddddd" width="450" height="300" style="fill: rgb(221, 221, 221); stroke: rgb(212, 91, 7);"></rect><text  id="v-30" font-size="34" xml:space="preserve" y="0.8em" font-weight="bold" text-anchor="middle" pointer-events="visible" font-family="Open Sans" fill="#d45b07" transform="matrix(1,0,0,1,225,84.4)" style="fill: rgb(212, 91, 7);"><tspan dy="0" >API</tspan></text><text  id="v-31" font-size="17" xml:space="preserve" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#d45b07" transform="matrix(1,0,0,1,225,125.2)" style="fill: rgb(212, 91, 7);"><tspan dy="0" >[Container: Docker - Go]</tspan></text><text  id="v-32" font-size="24" xml:space="preserve" display="none" y="0.8em" text-anchor="middle" font-family="Open Sans" fill="#d45b07" transform="matrix(1,0,0,1,225,145.6)" style="fill: rgb(212, 91, 7);"><tspan dy="0" style="fill-opacity: 0; stroke-opacity: 0;" >-</tspan></text><g  id="v-33" color="#d45b07" transform="matrix(1,0,0,1,222.5,268)" display="none" style="fill: rgb(212, 91, 7);"><g ></g><g ></g><g ></g><g ></g></g><image  id="v-34" xlink:href="https://static.structurizr.com/themes/amazon-web-services-2023.01.31/Res_AWS-Elastic-Beanstalk_Application_48_Light.png" width="60" height="60" opacity="1" transform="matrix(1,0,0,1,195,155.6)"></image></g></g><!--z-index:31--><g   id="j_2"  transform="translate(20,1986.6)"><g id="v-5"><text  id="v-6" font-size="22px" xml:space="preserve" y="0.8em" font-weight="normal" text-anchor="start" fill="#aaaaaa" pointer-events="none" display="block" font-family="Open Sans"><tspan dy="0"  display="block">Tuesday, March 4, 2025 at 12:30 PM Greenwich Mean Time</tspan></text></g></g><!--z-index:32--><g   id="j_1"  transform="translate(20,1943.3999999999999)"><g id="v-3"><text  id="v-4" font-size="36px" xml:space="preserve" y="0.8em" font-weight="normal" text-anchor="start" fill="#000000" pointer-events="none" display="block" font-family="Open Sans"><tspan dy="0"  display="block">[Deployment] Engage V2 - Production</tspan></text></g></g><!--z-index:33--><g   id="j_19" ><path  stroke="#707070" d="M 2255 1118 L 2705 1170" id="v-128" stroke-width="2" stroke-dasharray="30 30" fill="none" style="opacity: 1;"></path><path  fill="black" stroke="black" d="M 0 0 0 0" transform="matrix(0.993389585646587,0.1147916858004185,-0.1147916858004185,0.993389585646587,2255,1118)"></path><path  fill="#707070" stroke="#707070" d="M 20 0 L 0 10 L 20 20 z" id="v-130" transform="matrix(-0.9933895780038661,-0.11479175193933042,0.11479175193933042,-0.9933895780038661,2703.8521403700206,1179.933864080682)" style="opacity: 1;"></path><path  d="M 2255 1118 L 2705 1170" id="v-129" fill="none" style="cursor: default;"></path><g ><g id="v-122"  label-idx="0" cursor="move" transform="matrix(1,0,0,1,2480,1144)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-125" fill="#ffffff" rx="3" ry="3" pointer-events="none" width="182.3114776611328" height="60.85844802856445" transform="matrix(1,0,0,1,-91.1,-30.5)"></rect><text joint-selector="text" id="v-124" font-size="24px" xml:space="preserve" y="0.8em" fill="#707070" text-anchor="middle" pointer-events="none" font-family="Open Sans" font-weight="normal" transform="matrix(1,0,0,1,0,-25)"><tspan dy="0" >Reads from and</tspan><tspan dy="1.2em" x="0" >writes to</tspan></text></g><g id="v-123"  label-idx="1" cursor="move" transform="matrix(1,0,0,1,2470,1184.800048828125)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-127" fill="#ffffff" rx="3" ry="3"  width="0" height="0" transform="matrix(1,0,0,1,0,0)" display="none"></rect><text joint-selector="text" id="v-126" fill="#000000" font-size="14" text-anchor="middle" pointer-events="none"></text></g></g><g  display="none"></g><g  id="v-132" display="none"><g  id="v-120"><path  end="source" d="M 26 0 L 0 13 L 26 26 z"></path></g><g  id="v-121"><path  end="target" d="M 26 0 L 0 13 L 26 26 z"></path></g></g><g  id="v-131" display="none"><g  id="v-119" transform="translate(2294.7355834274504, 1122.5916674182831) "><g  event="remove"><circle r="11"></circle><path transform="scale(.8) translate(-16, -16)" d="M24.778,21.419 19.276,15.917 24.777,10.415 21.949,7.585 16.447,13.087 10.945,7.585 8.117,10.415 13.618,15.917 8.116,21.419 10.946,24.248 16.447,18.746 21.948,24.248z"></path><title>Remove link.</title></g><g  event="link:options"><circle r="11" transform="translate(25)"></circle><path fill="white" transform="scale(.55) translate(29, -16)" d="M31.229,17.736c0.064-0.571,0.104-1.148,0.104-1.736s-0.04-1.166-0.104-1.737l-4.377-1.557c-0.218-0.716-0.504-1.401-0.851-2.05l1.993-4.192c-0.725-0.91-1.549-1.734-2.458-2.459l-4.193,1.994c-0.647-0.347-1.334-0.632-2.049-0.849l-1.558-4.378C17.165,0.708,16.588,0.667,16,0.667s-1.166,0.041-1.737,0.105L12.707,5.15c-0.716,0.217-1.401,0.502-2.05,0.849L6.464,4.005C5.554,4.73,4.73,5.554,4.005,6.464l1.994,4.192c-0.347,0.648-0.632,1.334-0.849,2.05l-4.378,1.557C0.708,14.834,0.667,15.412,0.667,16s0.041,1.165,0.105,1.736l4.378,1.558c0.217,0.715,0.502,1.401,0.849,2.049l-1.994,4.193c0.725,0.909,1.549,1.733,2.459,2.458l4.192-1.993c0.648,0.347,1.334,0.633,2.05,0.851l1.557,4.377c0.571,0.064,1.148,0.104,1.737,0.104c0.588,0,1.165-0.04,1.736-0.104l1.558-4.377c0.715-0.218,1.399-0.504,2.049-0.851l4.193,1.993c0.909-0.725,1.733-1.549,2.458-2.458l-1.993-4.193c0.347-0.647,0.633-1.334,0.851-2.049L31.229,17.736zM16,20.871c-2.69,0-4.872-2.182-4.872-4.871c0-2.69,2.182-4.872,4.872-4.872c2.689,0,4.871,2.182,4.871,4.872C20.871,18.689,18.689,20.871,16,20.871z"></path><title>Link options.</title></g></g></g></g><!--z-index:34--><g   id="j_20" ><path  stroke="#707070" d="M 2255 965 L 2705 710" id="v-145" stroke-width="2" stroke-dasharray="30 30" fill="none" style="opacity: 1;"></path><path  fill="black" stroke="black" d="M 0 0 0 0" transform="matrix(0.8704367288733588,-0.4922803073740069,0.4922803073740069,0.8704367288733588,2255,964.75)"></path><path  fill="#707070" stroke="#707070" d="M 20 0 L 0 10 L 20 20 z" id="v-147" transform="matrix(-0.8704367288733585,0.4922803073740072,-0.4922803073740072,-0.8704367288733585,2709.922824659349,718.9543547423372)" style="opacity: 1;"></path><path  d="M 2255 965 L 2705 710" id="v-146" fill="none" style="cursor: default;"></path><g ><g id="v-139"  label-idx="0" cursor="move" transform="matrix(1,0,0,1,2480,837.5)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-142" fill="#ffffff" rx="3" ry="3" pointer-events="none" width="182.3114776611328" height="60.85844802856445" transform="matrix(1,0,0,1,-91.1,-30.5)"></rect><text joint-selector="text" id="v-141" font-size="24px" xml:space="preserve" y="0.8em" fill="#707070" text-anchor="middle" pointer-events="none" font-family="Open Sans" font-weight="normal" transform="matrix(1,0,0,1,0,-25)"><tspan dy="0" >Reads from and</tspan><tspan dy="1.2em" x="0" >writes to</tspan></text></g><g id="v-140"  label-idx="1" cursor="move" transform="matrix(1,0,0,1,2470,878.2999877929688)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-144" fill="#ffffff" rx="3" ry="3"  width="0" height="0" transform="matrix(1,0,0,1,0,0)" display="none"></rect><text joint-selector="text" id="v-143" fill="#000000" font-size="14" text-anchor="middle" pointer-events="none"></text></g></g><g  display="none"></g><g  id="v-149" display="none"><g  id="v-137"><path  end="source" d="M 26 0 L 0 13 L 26 26 z"></path></g><g  id="v-138"><path  end="target" d="M 26 0 L 0 13 L 26 26 z"></path></g></g><g  id="v-148" display="none"><g  id="v-136" transform="translate(2289.8008874339444, 945.2794971207649) "><g  event="remove"><circle r="11"></circle><path transform="scale(.8) translate(-16, -16)" d="M24.778,21.419 19.276,15.917 24.777,10.415 21.949,7.585 16.447,13.087 10.945,7.585 8.117,10.415 13.618,15.917 8.116,21.419 10.946,24.248 16.447,18.746 21.948,24.248z"></path><title>Remove link.</title></g><g  event="link:options"><circle r="11" transform="translate(25)"></circle><path fill="white" transform="scale(.55) translate(29, -16)" d="M31.229,17.736c0.064-0.571,0.104-1.148,0.104-1.736s-0.04-1.166-0.104-1.737l-4.377-1.557c-0.218-0.716-0.504-1.401-0.851-2.05l1.993-4.192c-0.725-0.91-1.549-1.734-2.458-2.459l-4.193,1.994c-0.647-0.347-1.334-0.632-2.049-0.849l-1.558-4.378C17.165,0.708,16.588,0.667,16,0.667s-1.166,0.041-1.737,0.105L12.707,5.15c-0.716,0.217-1.401,0.502-2.05,0.849L6.464,4.005C5.554,4.73,4.73,5.554,4.005,6.464l1.994,4.192c-0.347,0.648-0.632,1.334-0.849,2.05l-4.378,1.557C0.708,14.834,0.667,15.412,0.667,16s0.041,1.165,0.105,1.736l4.378,1.558c0.217,0.715,0.502,1.401,0.849,2.049l-1.994,4.193c0.725,0.909,1.549,1.733,2.459,2.458l4.192-1.993c0.648,0.347,1.334,0.633,2.05,0.851l1.557,4.377c0.571,0.064,1.148,0.104,1.737,0.104c0.588,0,1.165-0.04,1.736-0.104l1.558-4.377c0.715-0.218,1.399-0.504,2.049-0.851l4.193,1.993c0.909-0.725,1.733-1.549,2.458-2.458l-1.993-4.193c0.347-0.647,0.633-1.334,0.851-2.049L31.229,17.736zM16,20.871c-2.69,0-4.872-2.182-4.872-4.871c0-2.69,2.182-4.872,4.872-4.872c2.689,0,4.871,2.182,4.871,4.872C20.871,18.689,18.689,20.871,16,20.871z"></path><title>Link options.</title></g></g></g></g><!--z-index:35--><g   id="j_21" ><path  stroke="#707070" d="M 694 955 L 808 1058 C 812.6666666666665 1062.6666666666665 818.3333333333333 1065 825 1065 L 1055 1064" id="v-166" stroke-width="2" stroke-dasharray="30 30" fill="none" style="opacity: 1;"></path><path  fill="black" stroke="black" d="M 0 0 0 0" transform="matrix(0.738774573703176,0.6739526164721747,-0.6739526164721747,0.738774573703176,694.4199829101562,955)"></path><path  fill="#707070" stroke="#707070" d="M 20 0 L 0 10 L 20 20 z" id="v-168" transform="matrix(-0.9999907907379423,0.0042916709222402235,-0.0042916709222402235,-0.9999907907379423,1055.0429466869145,1073.969930651282)" style="opacity: 1;"></path><path  d="M 694 955 L 808 1058 C 812.6666666666665 1062.6666666666665 818.3333333333333 1065 825 1065 L 1055 1064" id="v-167" fill="none" style="cursor: default;"></path><g ><g id="v-157"  label-idx="0" cursor="move" transform="matrix(1,0,0,1,853.7333374023438,1064.8751220703125)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-161" fill="#ffffff" rx="3" ry="3" pointer-events="none" width="150.0135955810547" height="60.85844802856445" transform="matrix(1,0,0,1,-75.9,-30.5)"></rect><text joint-selector="text" id="v-160" font-size="24px" xml:space="preserve" y="0.8em" fill="#707070" text-anchor="middle" pointer-events="none" font-family="Open Sans" font-weight="normal" transform="matrix(1,0,0,1,0,-25)"><tspan dy="0" >Forwards API</tspan><tspan dy="1.2em" x="0" >requests to</tspan></text></g><g id="v-158"  label-idx="1" cursor="move" transform="matrix(1,0,0,1,853.7333374023438,1105.675048828125)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-163" fill="#ffffff" rx="3" ry="3" pointer-events="none" width="66.0821762084961" height="22.194310188293457" transform="matrix(1,0,0,1,-33.3,-11.1)"></rect><text joint-selector="text" id="v-162" font-size="17px" xml:space="preserve" y="0.8em" fill="#707070" text-anchor="middle" pointer-events="none" font-family="Open Sans" font-weight="normal" transform="matrix(1,0,0,1,0,-7.4)"><tspan dy="0" >[HTTPS]</tspan></text></g><g id="v-159"  label-idx="2" cursor="move" transform="matrix(1,0,0,1,843.7333374023438,1114.0750732421875)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-165" fill="#ffffff" rx="3" ry="3"  width="0" height="0" transform="matrix(1,0,0,1,0,0)" display="none"></rect><text joint-selector="text" id="v-164" fill="#000000" font-size="14" text-anchor="middle" pointer-events="none"></text></g></g><g  display="none"><g  transform="translate(815, 1065)" id="v-154"><circle  idx="0" r="10"></circle><path  idx="0" d="M16,5.333c-7.732,0-14,4.701-14,10.5c0,1.982,0.741,3.833,2.016,5.414L2,25.667l5.613-1.441c2.339,1.317,5.237,2.107,8.387,2.107c7.732,0,14-4.701,14-10.5C30,10.034,23.732,5.333,16,5.333z" transform="translate(5, -33)"></path><path  idx="0" transform="scale(.8) translate(9.5, -37)" d="M24.778,21.419 19.276,15.917 24.777,10.415 21.949,7.585 16.447,13.087 10.945,7.585 8.117,10.415 13.618,15.917 8.116,21.419 10.946,24.248 16.447,18.746 21.948,24.248z"><title>Remove vertex.</title></path></g></g><g  id="v-170" display="none"><g  id="v-155"><path  end="source" d="M 26 0 L 0 13 L 26 26 z"></path></g><g  id="v-156"><path  end="target" d="M 26 0 L 0 13 L 26 26 z"></path></g></g><g  id="v-169" display="none"><g  id="v-153" transform="translate(723.6799278099895, 981.8160751265693) "><g  event="remove"><circle r="11"></circle><path transform="scale(.8) translate(-16, -16)" d="M24.778,21.419 19.276,15.917 24.777,10.415 21.949,7.585 16.447,13.087 10.945,7.585 8.117,10.415 13.618,15.917 8.116,21.419 10.946,24.248 16.447,18.746 21.948,24.248z"></path><title>Remove link.</title></g><g  event="link:options"><circle r="11" transform="translate(25)"></circle><path fill="white" transform="scale(.55) translate(29, -16)" d="M31.229,17.736c0.064-0.571,0.104-1.148,0.104-1.736s-0.04-1.166-0.104-1.737l-4.377-1.557c-0.218-0.716-0.504-1.401-0.851-2.05l1.993-4.192c-0.725-0.91-1.549-1.734-2.458-2.459l-4.193,1.994c-0.647-0.347-1.334-0.632-2.049-0.849l-1.558-4.378C17.165,0.708,16.588,0.667,16,0.667s-1.166,0.041-1.737,0.105L12.707,5.15c-0.716,0.217-1.401,0.502-2.05,0.849L6.464,4.005C5.554,4.73,4.73,5.554,4.005,6.464l1.994,4.192c-0.347,0.648-0.632,1.334-0.849,2.05l-4.378,1.557C0.708,14.834,0.667,15.412,0.667,16s0.041,1.165,0.105,1.736l4.378,1.558c0.217,0.715,0.502,1.401,0.849,2.049l-1.994,4.193c0.725,0.909,1.549,1.733,2.459,2.458l4.192-1.993c0.648,0.347,1.334,0.633,2.05,0.851l1.557,4.377c0.571,0.064,1.148,0.104,1.737,0.104c0.588,0,1.165-0.04,1.736-0.104l1.558-4.377c0.715-0.218,1.399-0.504,2.049-0.851l4.193,1.993c0.909-0.725,1.733-1.549,2.458-2.458l-1.993-4.193c0.347-0.647,0.633-1.334,0.851-2.049L31.229,17.736zM16,20.871c-2.69,0-4.872-2.182-4.872-4.871c0-2.69,2.182-4.872,4.872-4.872c2.689,0,4.871,2.182,4.871,4.872C20.871,18.689,18.689,20.871,16,20.871z"></path><title>Link options.</title></g></g></g></g><!--z-index:36--><g   id="j_22" ><path  stroke="#707070" d="M 670 655 L 928 377 C 932.6666666666665 372.3333333333333 938.3333333333333 370 945 370 L 1055 372" id="v-187" stroke-width="2" stroke-dasharray="30 30" fill="none" style="opacity: 1;"></path><path  fill="black" stroke="black" d="M 0 0 0 0" transform="matrix(0.6814118794766175,-0.7319001643039461,0.7319001643039461,0.6814118794766175,669.6599731445312,655)"></path><path  fill="#707070" stroke="#707070" d="M 20 0 L 0 10 L 20 20 z" id="v-189" transform="matrix(-0.9998948906460482,-0.014498539923971926,0.014498539923971926,-0.9998948906460482,1054.8550621400334,381.73893552640334)" style="opacity: 1;"></path><path  d="M 670 655 L 928 377 C 932.6666666666665 372.3333333333333 938.3333333333333 370 945 370 L 1055 372" id="v-188" fill="none" style="cursor: default;"></path><g ><g id="v-178"  label-idx="0" cursor="move" transform="matrix(1,0,0,1,842.8468017578125,468.7542419433594)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-182" fill="#ffffff" rx="3" ry="3" pointer-events="none" width="137.91317749023438" height="60.85844802856445" transform="matrix(1,0,0,1,-69.8,-30.5)"></rect><text joint-selector="text" id="v-181" font-size="24px" xml:space="preserve" y="0.8em" fill="#707070" text-anchor="middle" pointer-events="none" font-family="Open Sans" font-weight="normal" transform="matrix(1,0,0,1,0,-25)"><tspan dy="0" >Forwards UI</tspan><tspan dy="1.2em" x="0" >requests to</tspan></text></g><g id="v-179"  label-idx="1" cursor="move" transform="matrix(1,0,0,1,842.8468017578125,509.5542297363281)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-184" fill="#ffffff" rx="3" ry="3" pointer-events="none" width="66.0821762084961" height="22.194310188293457" transform="matrix(1,0,0,1,-33.3,-11.1)"></rect><text joint-selector="text" id="v-183" font-size="17px" xml:space="preserve" y="0.8em" fill="#707070" text-anchor="middle" pointer-events="none" font-family="Open Sans" font-weight="normal" transform="matrix(1,0,0,1,0,-7.4)"><tspan dy="0" >[HTTPS]</tspan></text></g><g id="v-180"  label-idx="2" cursor="move" transform="matrix(1,0,0,1,832.8468017578125,517.9542236328125)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-186" fill="#ffffff" rx="3" ry="3"  width="0" height="0" transform="matrix(1,0,0,1,0,0)" display="none"></rect><text joint-selector="text" id="v-185" fill="#000000" font-size="14" text-anchor="middle" pointer-events="none"></text></g></g><g  display="none"><g  transform="translate(935, 370)" id="v-175"><circle  idx="0" r="10"></circle><path  idx="0" d="M16,5.333c-7.732,0-14,4.701-14,10.5c0,1.982,0.741,3.833,2.016,5.414L2,25.667l5.613-1.441c2.339,1.317,5.237,2.107,8.387,2.107c7.732,0,14-4.701,14-10.5C30,10.034,23.732,5.333,16,5.333z" transform="translate(5, -33)"></path><path  idx="0" transform="scale(.8) translate(9.5, -37)" d="M24.778,21.419 19.276,15.917 24.777,10.415 21.949,7.585 16.447,13.087 10.945,7.585 8.117,10.415 13.618,15.917 8.116,21.419 10.946,24.248 16.447,18.746 21.948,24.248z"><title>Remove vertex.</title></path></g></g><g  id="v-191" display="none"><g  id="v-176"><path  end="source" d="M 26 0 L 0 13 L 26 26 z"></path></g><g  id="v-177"><path  end="target" d="M 26 0 L 0 13 L 26 26 z"></path></g></g><g  id="v-190" display="none"><g  id="v-174" transform="translate(697.2099524773593, 625.6807488809851) "><g  event="remove"><circle r="11"></circle><path transform="scale(.8) translate(-16, -16)" d="M24.778,21.419 19.276,15.917 24.777,10.415 21.949,7.585 16.447,13.087 10.945,7.585 8.117,10.415 13.618,15.917 8.116,21.419 10.946,24.248 16.447,18.746 21.948,24.248z"></path><title>Remove link.</title></g><g  event="link:options"><circle r="11" transform="translate(25)"></circle><path fill="white" transform="scale(.55) translate(29, -16)" d="M31.229,17.736c0.064-0.571,0.104-1.148,0.104-1.736s-0.04-1.166-0.104-1.737l-4.377-1.557c-0.218-0.716-0.504-1.401-0.851-2.05l1.993-4.192c-0.725-0.91-1.549-1.734-2.458-2.459l-4.193,1.994c-0.647-0.347-1.334-0.632-2.049-0.849l-1.558-4.378C17.165,0.708,16.588,0.667,16,0.667s-1.166,0.041-1.737,0.105L12.707,5.15c-0.716,0.217-1.401,0.502-2.05,0.849L6.464,4.005C5.554,4.73,4.73,5.554,4.005,6.464l1.994,4.192c-0.347,0.648-0.632,1.334-0.849,2.05l-4.378,1.557C0.708,14.834,0.667,15.412,0.667,16s0.041,1.165,0.105,1.736l4.378,1.558c0.217,0.715,0.502,1.401,0.849,2.049l-1.994,4.193c0.725,0.909,1.549,1.733,2.459,2.458l4.192-1.993c0.648,0.347,1.334,0.633,2.05,0.851l1.557,4.377c0.571,0.064,1.148,0.104,1.737,0.104c0.588,0,1.165-0.04,1.736-0.104l1.558-4.377c0.715-0.218,1.399-0.504,2.049-0.851l4.193,1.993c0.909-0.725,1.733-1.549,2.458-2.458l-1.993-4.193c0.347-0.647,0.633-1.334,0.851-2.049L31.229,17.736zM16,20.871c-2.69,0-4.872-2.182-4.872-4.871c0-2.69,2.182-4.872,4.872-4.872c2.689,0,4.871,2.182,4.871,4.872C20.871,18.689,18.689,20.871,16,20.871z"></path><title>Link options.</title></g></g></g></g><!--z-index:37--><g   id="j_23" ><path  stroke="#707070" d="M 1740 375 L 1505 375" id="v-204" stroke-width="2" stroke-dasharray="30 30" fill="none" style="opacity: 1;"></path><path  fill="black" stroke="black" d="M 0 0 0 0" transform="matrix(-0.9999999999999999,1.224646799147353e-16,-1.224646799147353e-16,-0.9999999999999999,1740,375)"></path><path  fill="#707070" stroke="#707070" d="M 20 0 L 0 10 L 20 20 z" id="v-206" transform="matrix(0.9999999999999999,0,0,0.9999999999999999,1505,365)" style="opacity: 1;"></path><path  d="M 1740 375 L 1505 375" id="v-205" fill="none" style="cursor: default;"></path><g ><g id="v-198"  label-idx="0" cursor="move" transform="matrix(1,0,0,1,1622.5,375)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-201" fill="#ffffff" rx="3" ry="3" pointer-events="none" width="151.46572875976562" height="60.85844802856445" transform="matrix(1,0,0,1,-75.7,-30.5)"></rect><text joint-selector="text" id="v-200" font-size="24px" xml:space="preserve" y="0.8em" fill="#707070" text-anchor="middle" pointer-events="none" font-family="Open Sans" font-weight="normal" transform="matrix(1,0,0,1,0,-25)"><tspan dy="0" >Serves static</tspan><tspan dy="1.2em" x="0" >content from</tspan></text></g><g id="v-199"  label-idx="1" cursor="move" transform="matrix(1,0,0,1,1612.5,415.79998779296875)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-203" fill="#ffffff" rx="3" ry="3"  width="0" height="0" transform="matrix(1,0,0,1,0,0)" display="none"></rect><text joint-selector="text" id="v-202" fill="#000000" font-size="14" text-anchor="middle" pointer-events="none"></text></g></g><g  display="none"></g><g  id="v-208" display="none"><g  id="v-196"><path  end="source" d="M 26 0 L 0 13 L 26 26 z"></path></g><g  id="v-197"><path  end="target" d="M 26 0 L 0 13 L 26 26 z"></path></g></g><g  id="v-207" display="none"><g  id="v-195" transform="translate(1700, 375) "><g  event="remove"><circle r="11"></circle><path transform="scale(.8) translate(-16, -16)" d="M24.778,21.419 19.276,15.917 24.777,10.415 21.949,7.585 16.447,13.087 10.945,7.585 8.117,10.415 13.618,15.917 8.116,21.419 10.946,24.248 16.447,18.746 21.948,24.248z"></path><title>Remove link.</title></g><g  event="link:options"><circle r="11" transform="translate(25)"></circle><path fill="white" transform="scale(.55) translate(29, -16)" d="M31.229,17.736c0.064-0.571,0.104-1.148,0.104-1.736s-0.04-1.166-0.104-1.737l-4.377-1.557c-0.218-0.716-0.504-1.401-0.851-2.05l1.993-4.192c-0.725-0.91-1.549-1.734-2.458-2.459l-4.193,1.994c-0.647-0.347-1.334-0.632-2.049-0.849l-1.558-4.378C17.165,0.708,16.588,0.667,16,0.667s-1.166,0.041-1.737,0.105L12.707,5.15c-0.716,0.217-1.401,0.502-2.05,0.849L6.464,4.005C5.554,4.73,4.73,5.554,4.005,6.464l1.994,4.192c-0.347,0.648-0.632,1.334-0.849,2.05l-4.378,1.557C0.708,14.834,0.667,15.412,0.667,16s0.041,1.165,0.105,1.736l4.378,1.558c0.217,0.715,0.502,1.401,0.849,2.049l-1.994,4.193c0.725,0.909,1.549,1.733,2.459,2.458l4.192-1.993c0.648,0.347,1.334,0.633,2.05,0.851l1.557,4.377c0.571,0.064,1.148,0.104,1.737,0.104c0.588,0,1.165-0.04,1.736-0.104l1.558-4.377c0.715-0.218,1.399-0.504,2.049-0.851l4.193,1.993c0.909-0.725,1.733-1.549,2.458-2.458l-1.993-4.193c0.347-0.647,0.633-1.334,0.851-2.049L31.229,17.736zM16,20.871c-2.69,0-4.872-2.182-4.872-4.871c0-2.69,2.182-4.872,4.872-4.872c2.689,0,4.871,2.182,4.871,4.872C20.871,18.689,18.689,20.871,16,20.871z"></path><title>Link options.</title></g></g></g></g><!--z-index:38--><g   id="j_24" ><path  stroke="#707070" d="M 1505 1072 L 1805 1083" id="v-224" stroke-width="2" stroke-dasharray="30 30" fill="none" style="opacity: 1;"></path><path  fill="black" stroke="black" d="M 0 0 0 0" transform="matrix(0.9992532816040676,0.03863779501345657,-0.03863779501345657,0.9992532816040676,1505,1071.699951171875)"></path><path  fill="#707070" stroke="#707070" d="M 20 0 L 0 10 L 20 20 z" id="v-226" transform="matrix(-0.9992532772630235,-0.03863790728166924,0.03863790728166924,-0.9992532772630235,1804.613599988876,1093.292486064197)" style="opacity: 1;"></path><path  d="M 1505 1072 L 1805 1083" id="v-225" fill="none" style="cursor: default;"></path><g ><g id="v-215"  label-idx="0" cursor="move" transform="matrix(1,0,0,1,1655,1077.5)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-219" fill="#ffffff" rx="3" ry="3" pointer-events="none" width="131.95550537109375" height="60.85844802856445" transform="matrix(1,0,0,1,-65.8,-30.5)"></rect><text joint-selector="text" id="v-218" font-size="24px" xml:space="preserve" y="0.8em" fill="#707070" text-anchor="middle" pointer-events="none" font-family="Open Sans" font-weight="normal" transform="matrix(1,0,0,1,0,-25)"><tspan dy="0" >Forwards</tspan><tspan dy="1.2em" x="0" >requests to</tspan></text></g><g id="v-216"  label-idx="1" cursor="move" transform="matrix(1,0,0,1,1655,1118.300048828125)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-221" fill="#ffffff" rx="3" ry="3" pointer-events="none" width="66.0821762084961" height="22.194310188293457" transform="matrix(1,0,0,1,-33.3,-11.1)"></rect><text joint-selector="text" id="v-220" font-size="17px" xml:space="preserve" y="0.8em" fill="#707070" text-anchor="middle" pointer-events="none" font-family="Open Sans" font-weight="normal" transform="matrix(1,0,0,1,0,-7.4)"><tspan dy="0" >[HTTPS]</tspan></text></g><g id="v-217"  label-idx="2" cursor="move" transform="matrix(1,0,0,1,1645,1126.699951171875)" style="cursor: default; opacity: 1;"><rect joint-selector="rect" id="v-223" fill="#ffffff" rx="3" ry="3"  width="0" height="0" transform="matrix(1,0,0,1,0,0)" display="none"></rect><text joint-selector="text" id="v-222" fill="#000000" font-size="14" text-anchor="middle" pointer-events="none"></text></g></g><g  display="none"></g><g  id="v-228" display="none"><g  id="v-213"><path  end="source" d="M 26 0 L 0 13 L 26 26 z"></path></g><g  id="v-214"><path  end="target" d="M 26 0 L 0 13 L 26 26 z"></path></g></g><g  id="v-227" display="none"><g  id="v-212" transform="translate(1544.9731381937333, 1073.4656817337702) "><g  event="remove"><circle r="11"></circle><path transform="scale(.8) translate(-16, -16)" d="M24.778,21.419 19.276,15.917 24.777,10.415 21.949,7.585 16.447,13.087 10.945,7.585 8.117,10.415 13.618,15.917 8.116,21.419 10.946,24.248 16.447,18.746 21.948,24.248z"></path><title>Remove link.</title></g><g  event="link:options"><circle r="11" transform="translate(25)"></circle><path fill="white" transform="scale(.55) translate(29, -16)" d="M31.229,17.736c0.064-0.571,0.104-1.148,0.104-1.736s-0.04-1.166-0.104-1.737l-4.377-1.557c-0.218-0.716-0.504-1.401-0.851-2.05l1.993-4.192c-0.725-0.91-1.549-1.734-2.458-2.459l-4.193,1.994c-0.647-0.347-1.334-0.632-2.049-0.849l-1.558-4.378C17.165,0.708,16.588,0.667,16,0.667s-1.166,0.041-1.737,0.105L12.707,5.15c-0.716,0.217-1.401,0.502-2.05,0.849L6.464,4.005C5.554,4.73,4.73,5.554,4.005,6.464l1.994,4.192c-0.347,0.648-0.632,1.334-0.849,2.05l-4.378,1.557C0.708,14.834,0.667,15.412,0.667,16s0.041,1.165,0.105,1.736l4.378,1.558c0.217,0.715,0.502,1.401,0.849,2.049l-1.994,4.193c0.725,0.909,1.549,1.733,2.459,2.458l4.192-1.993c0.648,0.347,1.334,0.633,2.05,0.851l1.557,4.377c0.571,0.064,1.148,0.104,1.737,0.104c0.588,0,1.165-0.04,1.736-0.104l1.558-4.377c0.715-0.218,1.399-0.504,2.049-0.851l4.193,1.993c0.909-0.725,1.733-1.549,2.458-2.458l-1.993-4.193c0.347-0.647,0.633-1.334,0.851-2.049L31.229,17.736zM16,20.871c-2.69,0-4.872-2.182-4.872-4.871c0-2.69,2.182-4.872,4.872-4.872c2.689,0,4.871,2.182,4.871,4.872C20.871,18.689,18.689,20.871,16,20.871z"></path><title>Link options.</title></g></g></g></g><!--z-index:39--></g><g ></g><g ></g><g ></g></g></svg>