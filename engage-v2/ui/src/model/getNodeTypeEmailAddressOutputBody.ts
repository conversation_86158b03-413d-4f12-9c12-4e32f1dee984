/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { GetNodeTypeEmailAddressOutputBodyActivityLogs } from "./getNodeTypeEmailAddressOutputBodyActivityLogs";
import type { NodeEmailAddress } from "./nodeEmailAddress";

export interface GetNodeTypeEmailAddressOutputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  /** Activity Logs */
  activity_logs?: GetNodeTypeEmailAddressOutputBodyActivityLogs;
  node: NodeEmailAddress;
}
