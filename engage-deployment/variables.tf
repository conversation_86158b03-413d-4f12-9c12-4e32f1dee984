# ***************************************************************
# * PER INSTANCE Configuration values
#   Make sure these are unique enough per instance
# ***************************************************************

variable "SUBDOMAIN_NAME" {
  type        = string
  default     = "engage"
  description = "Subdomain"
}

variable "DOMAIN_NAME" {
  type        = string
  default     = "aaro.sh"
  description = "Root domain name"
}

variable "UI_AZURE_APP_ID" {
  type        = string
  default     = "xxx"
  description = "Azure UI App ID"
}

variable "UI_AZURE_API_SCOPE" {
  type        = string
  default     = "xxx"
  description = "Azure UI App ID"
}

variable "SSO_AZURE_TENANT_ID" {
  type        = string
  default     = "xxx"
  description = "Azure Tenant ID for SSO"
}

variable "SSO_AZURE_API_APP_ID" {
  type        = string
  default     = "xxx"
  description = "Azure API App ID for SSO"
}

variable "SSO_AZURE_API_CLIENT_SECRET" {
  type        = string
  default     = "xxx"
  description = "Azure API App Secret for SSO"
}

variable "ENGAGE_AZURE_STANDARD_USERS_GRP_ID" {
  type        = string
  default     = "xxx"
  description = "Azure Group ID for Standard Users"
}

variable "ENGAGE_AZURE_ADMIN_USERS_GRP_ID" {
  type        = string
  default     = "xxx"
  description = "Azure Group ID for Admin Users"
}

variable "NETWORK_VPC_ID" {
  type        = string
  default     = "vpc-097bd5f8a6abe10b3"
  description = "VPC ID of the CDI connected Aviatrix network"
}

variable "NETWORK_SUBNET_1_ID" {
  type        = string
  default     = "subnet-07fe763688f9481a1"
  description = "Subnet 1 ID of the VPC"
}

variable "NETWORK_SUBNET_2_ID" {
  type        = string
  default     = "subnet-0d3782ba99b7733ca"
  description = "Subnet 2 ID of the VPC"
}

variable "ssh_publickey" {
  type    = string
  default = "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIEyNKU9gyriK3BToDxzpwKgp6/yYmZMa9XmnHUmo+eIW adrian@aaro-uki-kx2v"
}

variable "AWS_REGION" {
  type        = string
  default     = "eu-west-2"
  description = "AWS Region to deploy to"
}

variable "AWS_ACCESS_KEY_ID" {
  type        = string
  default     = ""
  description = "Access Key for AWS Deployment Account"
}

variable "AWS_SECRET_ACCESS_KEY" {
  type        = string
  default     = ""
  description = "Access Secret for AWS Deployment Account"
}

variable "person_poc" {
  type        = string
  default     = "Adrian Lewis"
  description = "Point of contact for technical issues"
}

variable "person_chargeback" {
  type        = string
  default     = "Carl Latimer"
  description = "Point of contact for chargeback"
}

# ***************************************************************
# * Don't worry too much about changing everything below.
#   Defaults are mostly fine
# ***************************************************************


# ***************************************************************
# * Application Deployment Variables
# ***************************************************************

variable "IMAGE_NAME" {
  type    = string
  default = "registry.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api"
}

variable "VERSION_TAG" {
  type    = string
  default = "latest"
}

variable "APP_ENVIRONMENT" {
  type    = string
  default = "prod"
}

variable "API_SUBDOMAIN" {
  type    = string
  default = "api"
}

variable "AWS_ROOT_EMAIL" {
  type = string
  description = "This email address forms the main alias of engagement account emails"
}

# ***************************************************************
# * GitLab Variables
# ***************************************************************


variable "GITLAB_PROJECT_ID" {
  type        = number
  description = "The Project ID of the Engage-v2 codebase"
  default     = 1465
}

variable "GITLAB_BASEURL" {
  type        = string
  description = "The URL of CDI's GitLab"
  default     = "https://gitlab.cyberdefense.global"
}

variable "GITLAB_ACCESS_TOKEN" {
  type        = string
  default     = ""
  description = "Access Token to pull the UI release"
}

variable "GITLAB_DOCKER_ACCESS_TOKEN" {
  type        = string
  default     = ""
  description = "Docker login token to pull the API image"
}


# ***************************************************************
# * Infrastructure Variables
# ***************************************************************

variable "ssh_listener_enabled" {
  type        = bool
  default     = true
  description = "Should SSH be enabled?"
}

variable "mq_instance" {
  type        = string
  default     = "mq.t3.micro"
  description = "Instance size of the database"
}

variable "db_size" {
  type        = string
  default     = "50"
  description = "Storage space available to the Database"
}

variable "db_instance" {
  type        = string
  default     = "db.t3.medium"
  description = "Instance size of the database"
}

variable "ec2_instance" {
  type        = string
  default     = "t3.large"
  description = "Instance size of the instance hosting the tasks. Make this large enough to enable multiple tasks to permit uninterrupted task changes"
}

variable "solution_stack_name" {
  type        = string
  default     = "64bit Amazon Linux 2023 v4.4.4 running Docker"
  description = "Solution stack name for Beanstalk"
}
