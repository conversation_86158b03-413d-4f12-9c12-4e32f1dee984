workspace {

    model {
        u = person "User"
        s = softwareSystem "Engage V2" {
            webapp = container "API" "" "Docker - Go" {
                tags "Amazon Web Services - AWS Elastic Beanstalk Application"
            }
            frontend = container "Frontend UI" "" "Vite React"
            database = container "Database" "" "Relational database schema"
            rabbitmq = container "Rabbit MQ" "" "Rabbit MQ"
        }

        u -> webapp "Uses"
        webapp -> database "Reads from and writes to"
        webapp -> rabbitmq "Reads from and writes to"
        
        production = deploymentEnvironment "Production" {
            deploymentNode "Amazon Web Services" {
                tags "Amazon Web Services - Cloud"
                
                deploymentNode "EU-West-2" {
                    tags "Amazon Web Services - Region"
                
                    route53 = infrastructureNode "Route 53" {
                        tags "Amazon Web Services - Route 53"
                    }

                    deploymentNode "S3" {
                        tags "Amazon Web Services - Simple Storage Service S3 Standard"

                        frontend_s3 = infrastructureNode "S3 Bucket" {
                            tags "Amazon Web Services - Simple Storage Service Bucket"
                        }
                    }

                    deploymentNode "Aviatrix Private VPC" {
                        tags "Amazon Web Services - VPC Virtual private cloud VPC"

                        frontend_cf = infrastructureNode "CloudFront" {
                            tags "Amazon Web Services - CloudFront"
                        }

                        deploymentNode "Amazon Elastic Beanstalk" {
                            tags "Amazon Web Services - Elastic Beanstalk"

                            deploymentNode "Autoscaling Group" {
                                tags "Amazon Web Services - Auto Scaling"
                            
                                deploymentNode "Amazon Linux 2023 Server" {
                                    tags "Amazon Web Services - Elastic Beanstalk container"
                                    
                                    webApplicationInstance = containerInstance webapp {
                                        tags "Amazon Web Services - Elastic Beanstalk Application"
                                    }
                                }
                            }
                            elb = infrastructureNode "Elastic Load Balancer" {
                                tags "Amazon Web Services - Elastic Load Balancing"
                            }
                        }

                        deploymentNode "PostgreSQL RDS" {
                            tags "Amazon Web Services - RDS"
                            containerInstance database
                        }

                        deploymentNode "Rabbit MQ" {
                            tags "Amazon Web Services - MQ"
                                
                            containerInstance rabbitmq
                        }
                    }
                }
            }
            
            route53 -> elb "Forwards API requests to" "HTTPS"
            route53 -> frontend_cf "Forwards UI requests to" "HTTPS"
            frontend_s3 -> frontend_cf "Serves static content from"
            elb -> webApplicationInstance "Forwards requests to" "HTTPS"
        }
    }

    views {
        deployment s production {
            include *
            // autoLayout lr
        }

        theme https://static.structurizr.com/themes/amazon-web-services-2023.01.31/theme.json
    }
    
}
