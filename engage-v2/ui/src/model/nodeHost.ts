/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { NodeHostAlternativeNames } from "./nodeHostAlternativeNames";
import type { NodeHostIpAddresses } from "./nodeHostIpAddresses";

export interface NodeHost {
  alternative_names: NodeHostAlternativeNames;
  ip_addresses: NodeHostIpAddresses;
  name: string;
  /** Node ID */
  node_id: string;
}
