/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { GetNodeTypeUrlOutputBodyActivityLogs } from "./getNodeTypeUrlOutputBodyActivityLogs";
import type { NodeUrl } from "./nodeUrl";

export interface GetNodeTypeUrlOutputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  /** Activity Logs */
  activity_logs?: GetNodeTypeUrlOutputBodyActivityLogs;
  node: NodeUrl;
}
