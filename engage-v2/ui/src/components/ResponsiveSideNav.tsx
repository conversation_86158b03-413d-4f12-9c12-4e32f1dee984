import { HiX } from "react-icons/hi";

import SideNav from "./SideNav";

export interface ResponsiveSideNavProps {
  toggleSideNav: () => void;
  isSideNavOpen: boolean;
}

export default function ResponsiveSideNav({
  toggleSideNav,
  isSideNavOpen,
}: ResponsiveSideNavProps) {
  return (
    <>
      <div
        className={`fixed inset-0 z-30 bg-black/25 transition-opacity duration-300 lg:hidden ${
          isSideNavOpen ? "opacity-100" : "pointer-events-none opacity-0"
        }`}
        onClick={toggleSideNav}
        aria-hidden="true"
      />

      <div
        className={`fixed inset-y-0 left-0 z-40 w-64 transform transition-transform duration-300 lg:relative lg:z-auto lg:translate-x-0 ${
          isSideNavOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <div
          className="dark:bg-darkbg flex h-full flex-col bg-white"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex items-center justify-end p-4 lg:hidden">
            <button
              onClick={toggleSideNav}
              className="rounded-md p-1 text-gray-700 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700"
            >
              <HiX className="h-6 w-6" />
            </button>
          </div>
          <SideNav />
        </div>
      </div>
    </>
  );
}
